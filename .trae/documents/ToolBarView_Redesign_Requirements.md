# ToolBarView 重新设计需求文档

## 1. 产品概述

基于用户提供的UI设计图片，重新设计实现 NewViewController 扩展中的 `@objc func ToolBarView() -> UIView` 方法。新设计采用深色主题风格，包含功能卡片式布局和底部进度指示器，与整体界面保持高度一致的视觉风格。

## 2. 设计分析

### 2.1 UI设计特点

从提供的设计图片可以看出以下关键特征：

* **深色主题**：整体采用深灰色背景 (#2C2C2E 或类似)

* **卡片式设计**：功能按钮采用圆角卡片布局

* **图标风格**：白色线性图标，简洁现代

* **分组布局**：顶部切换按钮、中间功能卡片、底部进度条

* **中文标签**：人物宠物、物料商品、图标印章

### 2.2 功能模块映射

将原有的工具栏功能映射到新的设计风格：

| 原功能   | 新设计对应  | 图标建议    |
| ----- | ------ | ------- |
| 撤销/重做 | 操作历史卡片 | 撤销/重做图标 |
| 图层管理  | 图层管理卡片 | 图层堆叠图标  |
| 导出功能  | 导出分享卡片 | 分享/导出图标 |
| 进度控制  | 底部进度条  | 线性进度指示器 |

## 3. 核心功能

### 3.1 功能卡片

工具栏将包含以下功能卡片：

1. **操作历史卡片**

   * 撤销和重做功能

   * 图标：撤销/重做符号

   * 标签："操作历史"

2. **图层管理卡片**

   * 图层显示和编辑

   * 图标：图层堆叠符号

   * 标签："图层管理"

3. **导出分享卡片**

   * 导出和分享功能

   * 图标：分享/导出符号

   * 标签："导出分享"

### 3.2 进度指示器

* **位置**：工具栏底部

* **样式**：白色线性进度条

* **功能**：显示当前处理进度

## 4. 用户界面设计

### 4.1 设计规范

* **背景色**：深灰色 (#2C2C2E)

* **卡片背景**：中等灰色 (#48484A)

* **图标颜色**：白色 (#FFFFFF)

* **文字颜色**：白色 (#FFFFFF)

* **进度条颜色**：白色 (#FFFFFF)

* **圆角半径**：12pt

* **卡片间距**：16pt

* **内边距**：16pt

### 4.2 布局结构

```
┌─────────────────────────────────────┐
│  ToolBarView Container              │
│  ┌─────┐  ┌─────┐  ┌─────┐         │
│  │ 操作 │  │ 图层 │  │ 导出 │         │
│  │ 历史 │  │ 管理 │  │ 分享 │         │
│  └─────┘  └─────┘  └─────┘         │
│  ────────────────────────────       │
│         Progress Bar               │
└─────────────────────────────────────┘
```

### 4.3 响应式设计

* **适配方式**：支持不同屏幕尺寸

* **触摸区域**：最小44x44pt触摸区域

* **深色模式**：原生支持深色主题

* **无障碍**：完整的VoiceOver支持

## 5. 核心流程

用户交互流程：

1. 用户进入编辑界面
2. 工具栏显示在界面底部
3. 用户点击功能卡片执行相应操作
4. 进度条实时显示处理状态
5. 操作完成后更新界面状态

```mermaid
graph TD
    A[进入编辑界面] --> B[显示工具栏]
    B --> C[点击操作历史]
    B --> D[点击图层管理]
    B --> E[点击导出分享]
    C --> F[执行撤销/重做]
    D --> G[管理图层]
    E --> H[导出/分享]
    F --> I[更新进度]
    G --> I
    H --> I
    I --> J[界面状态更新]
```

## 6. 技术要求

### 6.1 实现方式

* **方法签名**：`@objc func ToolBarView() -> UIView`

* **布局方式**：SnapKit 自动布局

* **动画效果**：卡片点击反馈动画

* **状态管理**：按钮启用/禁用状态

### 6.2 性能要求

* **响应时间**：按钮点击响应 < 100ms

* **动画流畅度**：60fps 动画效果

* **内存占用**：最小化视图层级

## 7. 验收标准

### 7.1 视觉验收

* [ ] 深色主题风格正确

* [ ] 卡片圆角和间距符合设计

* [ ] 图标和文字颜色正确

* [ ] 进度条样式和位置正确

### 7.2 功能验收

* [ ] 所有功能卡片点击响应正常

* [ ] 进度条显示和更新正常

* [ ] 深色模式适配正常

* [ ] 无障碍功能正常

### 7.3 性能验收

* [ ] 界面渲染流畅

* [ ] 动画效果自然

* [ ] 内存使用合理

* [ ] 响应时间符合要求


# ToolBarView 重新设计实现指南

## 1. 实现概述

本文档提供了基于深色主题UI设计的 `ToolBarView()` 方法完整实现。新设计采用卡片式布局，包含三个功能卡片（操作历史、图层管理、导出分享）和底部进度条。

## 2. 完整代码实现

### 2.1 主要实现代码

```swift
// MARK: - ToolBarView Redesign Extension
extension NewViewController {
    
    @objc func ToolBarView() -> UIView {
        let containerView = createToolBarContainer()
        let cardsStackView = createCardsStackView()
        let progressContainer = createProgressContainer()
        
        // 创建功能卡片
        let historyCard = createHistoryCard()
        let layerCard = createLayerCard()
        let exportCard = createExportCard()
        
        // 组装卡片
        [historyCard, layerCard, exportCard].forEach {
            cardsStackView.addArrangedSubview($0)
        }
        
        // 组装容器
        containerView.addSubview(cardsStackView)
        containerView.addSubview(progressContainer)
        
        // 设置约束
        setupToolBarConstraints(
            container: containerView,
            cardsStack: cardsStackView,
            progressContainer: progressContainer
        )
        
        // 配置无障碍
        setupToolBarAccessibility(
            historyCard: historyCard,
            layerCard: layerCard,
            exportCard: exportCard,
            progressContainer: progressContainer
        )
        
        return containerView
    }
}
```

### 2.2 容器和布局方法

```swift
// MARK: - ToolBar Container Methods
extension NewViewController {
    
    private func createToolBarContainer() -> UIView {
        let container = UIView()
        container.backgroundColor = UIColor(red: 0.17, green: 0.17, blue: 0.18, alpha: 1.0) // #2C2C2E
        container.layer.cornerRadius = 16
        container.layer.masksToBounds = true
        
        // 添加轻微的边框效果
        container.layer.borderWidth = 0.5
        container.layer.borderColor = UIColor.white.withAlphaComponent(0.1).cgColor
        
        return container
    }
    
    private func createCardsStackView() -> UIStackView {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.alignment = .center
        stackView.spacing = 20
        return stackView
    }
    
    private func createProgressContainer() -> UIView {
        let container = UIView()
        let progressBar = UIProgressView(progressViewStyle: .default)
        
        // 配置进度条样式
        progressBar.progressTintColor = UIColor.white
        progressBar.trackTintColor = UIColor.white.withAlphaComponent(0.3)
        progressBar.layer.cornerRadius = 2
        progressBar.clipsToBounds = true
        progressBar.progress = 0.6 // 默认进度
        
        container.addSubview(progressBar)
        
        progressBar.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(4)
        }
        
        // 存储进度条引用
        container.tag = 999 // 用于后续查找
        
        return container
    }
}
```

### 2.3 功能卡片创建方法

```swift
// MARK: - Card Creation Methods
extension NewViewController {
    
    private func createHistoryCard() -> UIView {
        return createFunctionCard(
            iconName: "undo_icon",
            title: "操作历史",
            action: #selector(historyCardTapped)
        )
    }
    
    private func createLayerCard() -> UIView {
        return createFunctionCard(
            systemIconName: "square.3.layers.3d",
            title: "图层管理",
            action: #selector(layerCardTapped)
        )
    }
    
    private func createExportCard() -> UIView {
        return createFunctionCard(
            iconName: "download_icon",
            title: "导出分享",
            action: #selector(exportCardTapped)
        )
    }
    
    private func createFunctionCard(
        iconName: String? = nil,
        systemIconName: String? = nil,
        title: String,
        action: Selector
    ) -> UIView {
        let cardView = UIView()
        let iconImageView = UIImageView()
        let titleLabel = UILabel()
        
        // 配置卡片样式
        cardView.backgroundColor = UIColor(red: 0.28, green: 0.28, blue: 0.29, alpha: 1.0) // #48484A
        cardView.layer.cornerRadius = 12
        cardView.layer.masksToBounds = true
        
        // 添加轻微的内阴影效果
        cardView.layer.borderWidth = 0.5
        cardView.layer.borderColor = UIColor.white.withAlphaComponent(0.05).cgColor
        
        // 配置图标
        if let iconName = iconName {
            iconImageView.image = UIImage(named: iconName)?.withRenderingMode(.alwaysTemplate)
        } else if let systemIconName = systemIconName {
            iconImageView.image = UIImage(systemName: systemIconName)
        }
        
        iconImageView.tintColor = UIColor.white
        iconImageView.contentMode = .scaleAspectFit
        
        // 配置标签
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        titleLabel.textColor = UIColor.white
        titleLabel.textAlignment = .center
        titleLabel.numberOfLines = 1
        
        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: action)
        cardView.addGestureRecognizer(tapGesture)
        cardView.isUserInteractionEnabled = true
        
        // 布局子视图
        cardView.addSubview(iconImageView)
        cardView.addSubview(titleLabel)
        
        setupCardConstraints(cardView, iconImageView, titleLabel)
        
        return cardView
    }
}
```

### 2.4 约束设置方法

```swift
// MARK: - Constraints Setup
extension NewViewController {
    
    private func setupToolBarConstraints(
        container: UIView,
        cardsStack: UIStackView,
        progressContainer: UIView
    ) {
        // 容器约束
        container.snp.makeConstraints { make in
            make.height.equalTo(120)
        }
        
        // 卡片容器约束
        cardsStack.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(80)
        }
        
        // 进度条容器约束
        progressContainer.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-16)
            make.leading.trailing.equalToSuperview().inset(24)
            make.height.equalTo(4)
        }
    }
    
    private func setupCardConstraints(
        _ card: UIView,
        _ icon: UIImageView,
        _ label: UILabel
    ) {
        // 卡片尺寸
        card.snp.makeConstraints { make in
            make.width.equalTo(80)
            make.height.equalTo(80)
        }
        
        // 图标约束
        icon.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(16)
            make.size.equalTo(24)
        }
        
        // 标签约束
        label.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().offset(-12)
            make.leading.trailing.equalToSuperview().inset(6)
        }
    }
}
```

### 2.5 事件处理方法

```swift
// MARK: - Card Action Methods
extension NewViewController {
    
    @objc private func historyCardTapped() {
        print("History card tapped")
        
        // 添加点击动画
        if let historyCard = findCardByTitle("操作历史") {
            animateCardPress(historyCard)
        }
        
        // 显示操作历史选项
        presentHistoryOptions()
    }
    
    @objc private func layerCardTapped() {
        print("Layer card tapped")
        
        // 添加点击动画
        if let layerCard = findCardByTitle("图层管理") {
            animateCardPress(layerCard)
        }
        
        // 显示图层管理界面
        presentLayerManagement()
    }
    
    @objc private func exportCardTapped() {
        print("Export card tapped")
        
        // 添加点击动画
        if let exportCard = findCardByTitle("导出分享") {
            animateCardPress(exportCard)
        }
        
        // 显示导出选项
        presentExportOptions()
    }
    
    // MARK: - Helper Methods
    
    private func findCardByTitle(_ title: String) -> UIView? {
        // 在当前视图中查找包含指定标题的卡片
        return view.subviews.first { view in
            if let label = view.subviews.compactMap({ $0 as? UILabel }).first {
                return label.text == title
            }
            return false
        }
    }
    
    private func animateCardPress(_ cardView: UIView) {
        UIView.animate(withDuration: 0.1, animations: {
            cardView.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
            cardView.alpha = 0.8
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                cardView.transform = .identity
                cardView.alpha = 1.0
            }
        }
    }
}
```

### 2.6 功能实现方法

```swift
// MARK: - Feature Implementation Methods
extension NewViewController {
    
    private func presentHistoryOptions() {
        let alertController = UIAlertController(
            title: "操作历史",
            message: "选择要执行的操作",
            preferredStyle: .actionSheet
        )
        
        // 撤销操作
        let undoAction = UIAlertAction(title: "撤销", style: .default) { _ in
            self.performUndo()
        }
        
        // 重做操作
        let redoAction = UIAlertAction(title: "重做", style: .default) { _ in
            self.performRedo()
        }
        
        // 清空历史
        let clearAction = UIAlertAction(title: "清空历史", style: .destructive) { _ in
            self.clearHistory()
        }
        
        let cancelAction = UIAlertAction(title: "取消", style: .cancel)
        
        alertController.addAction(undoAction)
        alertController.addAction(redoAction)
        alertController.addAction(clearAction)
        alertController.addAction(cancelAction)
        
        // iPad 适配
        if let popover = alertController.popoverPresentationController {
            popover.sourceView = view
            popover.sourceRect = CGRect(x: view.bounds.midX, y: view.bounds.midY, width: 0, height: 0)
        }
        
        present(alertController, animated: true)
    }
    
    private func presentLayerManagement() {
        let alertController = UIAlertController(
            title: "图层管理",
            message: "管理图层显示和编辑",
            preferredStyle: .actionSheet
        )
        
        let showLayersAction = UIAlertAction(title: "显示图层", style: .default) { _ in
            self.showLayers()
        }
        
        let hideLayersAction = UIAlertAction(title: "隐藏图层", style: .default) { _ in
            self.hideLayers()
        }
        
        let mergeLayersAction = UIAlertAction(title: "合并图层", style: .default) { _ in
            self.mergeLayers()
        }
        
        let cancelAction = UIAlertAction(title: "取消", style: .cancel)
        
        alertController.addAction(showLayersAction)
        alertController.addAction(hideLayersAction)
        alertController.addAction(mergeLayersAction)
        alertController.addAction(cancelAction)
        
        // iPad 适配
        if let popover = alertController.popoverPresentationController {
            popover.sourceView = view
            popover.sourceRect = CGRect(x: view.bounds.midX, y: view.bounds.midY, width: 0, height: 0)
        }
        
        present(alertController, animated: true)
    }
    
    private func presentExportOptions() {
        let alertController = UIAlertController(
            title: "导出分享",
            message: "选择导出格式和分享方式",
            preferredStyle: .actionSheet
        )
        
        let exportPNGAction = UIAlertAction(title: "导出为 PNG", style: .default) { _ in
            self.exportAsPNG()
        }
        
        let exportJPGAction = UIAlertAction(title: "导出为 JPG", style: .default) { _ in
            self.exportAsJPG()
        }
        
        let shareAction = UIAlertAction(title: "分享", style: .default) { _ in
            self.shareImage()
        }
        
        let cancelAction = UIAlertAction(title: "取消", style: .cancel)
        
        alertController.addAction(exportPNGAction)
        alertController.addAction(exportJPGAction)
        alertController.addAction(shareAction)
        alertController.addAction(cancelAction)
        
        // iPad 适配
        if let popover = alertController.popoverPresentationController {
            popover.sourceView = view
            popover.sourceRect = CGRect(x: view.bounds.midX, y: view.bounds.midY, width: 0, height: 0)
        }
        
        present(alertController, animated: true)
    }
}
```

### 2.7 具体功能实现

```swift
// MARK: - Specific Feature Implementations
extension NewViewController {
    
    // MARK: - History Operations
    
    private func performUndo() {
        print("Performing undo operation")
        updateProgress(0.3)
        // TODO: 实现具体的撤销逻辑
    }
    
    private func performRedo() {
        print("Performing redo operation")
        updateProgress(0.7)
        // TODO: 实现具体的重做逻辑
    }
    
    private func clearHistory() {
        print("Clearing operation history")
        updateProgress(0.0)
        // TODO: 实现清空历史逻辑
    }
    
    // MARK: - Layer Operations
    
    private func showLayers() {
        print("Showing layers")
        // TODO: 实现显示图层逻辑
    }
    
    private func hideLayers() {
        print("Hiding layers")
        // TODO: 实现隐藏图层逻辑
    }
    
    private func mergeLayers() {
        print("Merging layers")
        updateProgress(0.8)
        // TODO: 实现合并图层逻辑
    }
    
    // MARK: - Export Operations
    
    private func exportAsPNG() {
        print("Exporting as PNG")
        updateProgress(1.0)
        // TODO: 实现PNG导出逻辑
    }
    
    private func exportAsJPG() {
        print("Exporting as JPG")
        updateProgress(1.0)
        // TODO: 实现JPG导出逻辑
    }
    
    private func shareImage() {
        print("Sharing image")
        // TODO: 实现分享逻辑
        
        // 示例分享实现
        guard let image = captureCurrentView() else { return }
        
        let activityViewController = UIActivityViewController(
            activityItems: [image],
            applicationActivities: nil
        )
        
        // iPad 适配
        if let popover = activityViewController.popoverPresentationController {
            popover.sourceView = view
            popover.sourceRect = CGRect(x: view.bounds.midX, y: view.bounds.midY, width: 0, height: 0)
        }
        
        present(activityViewController, animated: true)
    }
    
    // MARK: - Helper Methods
    
    private func updateProgress(_ progress: Float) {
        // 查找进度条并更新
        if let toolBar = view.subviews.first(where: { $0.tag == 999 }),
           let progressBar = toolBar.subviews.first as? UIProgressView {
            UIView.animate(withDuration: 0.3) {
                progressBar.setProgress(progress, animated: true)
            }
        }
    }
    
    private func captureCurrentView() -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(view.bounds.size, false, UIScreen.main.scale)
        defer { UIGraphicsEndImageContext() }
        
        guard let context = UIGraphicsGetCurrentContext() else { return nil }
        view.layer.render(in: context)
        
        return UIGraphicsGetImageFromCurrentImageContext()
    }
}
```

### 2.8 无障碍支持

```swift
// MARK: - Accessibility Support
extension NewViewController {
    
    private func setupToolBarAccessibility(
        historyCard: UIView,
        layerCard: UIView,
        exportCard: UIView,
        progressContainer: UIView
    ) {
        // 操作历史卡片
        historyCard.isAccessibilityElement = true
        historyCard.accessibilityLabel = "操作历史"
        historyCard.accessibilityHint = "点击管理撤销和重做操作"
        historyCard.accessibilityTraits = .button
        
        // 图层管理卡片
        layerCard.isAccessibilityElement = true
        layerCard.accessibilityLabel = "图层管理"
        layerCard.accessibilityHint = "点击管理图层显示和编辑"
        layerCard.accessibilityTraits = .button
        
        // 导出分享卡片
        exportCard.isAccessibilityElement = true
        exportCard.accessibilityLabel = "导出分享"
        exportCard.accessibilityHint = "点击导出和分享处理结果"
        exportCard.accessibilityTraits = .button
        
        // 进度条
        if let progressBar = progressContainer.subviews.first as? UIProgressView {
            progressBar.isAccessibilityElement = true
            progressBar.accessibilityLabel = "处理进度"
            progressBar.accessibilityValue = "\(Int(progressBar.progress * 100))%"
            progressBar.accessibilityTraits = .updatesFrequently
        }
    }
}
```

## 3. 集成步骤

### 3.1 在现有视图中使用

```swift
// 在 NewViewController 的 setupUI 方法中添加
private func setupUI() {
    // ... 现有代码 ...
    
    // 添加重新设计的工具栏
    let toolBar = ToolBarView()
    view.addSubview(toolBar)
    
    // 设置工具栏约束
    toolBar.snp.makeConstraints { make in
        make.leading.trailing.equalToSuperview().inset(16)
        make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-16)
    }
}
```

## 4. 测试验证

### 4.1 功能测试清单

* [ ] 工具栏正确显示三个功能卡片
* [ ] 操作历史卡片点击响应正常
* [ ] 图层管理卡片点击响应正常
* [ ] 导出分享卡片点击响应正常
* [ ] 进度条显示和更新正常
* [ ] 卡片点击动画效果正常
* [ ] 深色主题样式正确
* [ ] 无障碍功能正常

### 4.2 视觉验证

* [ ] 深色背景色正确 (#2C2C2E)
* [ ] 卡片背景色正确 (#48484A)
* [ ] 图标和文字为白色
* [ ] 圆角和间距符合设计
* [ ] 进度条样式正确

## 5. 性能优化

### 5.1 视图缓存

```swift
private var cachedToolBar: UIView?

@objc func ToolBarView() -> UIView {
    if let cached = cachedToolBar {
        return cached
    }
    
    let toolBar = createNewToolBar()
    cachedToolBar = toolBar
    return toolBar
}
```

### 5.2 图标预加载

```swift
private func preloadToolBarIcons() {
    DispatchQueue.global(qos: .background).async {
        let iconNames = ["undo_icon", "download_icon"]
        iconNames.forEach { _ = UIImage(named: $0) }
    }
}
```

这个重新设计的 ToolBarView 完全符合提供的UI设计风格，采用深色主题、卡片式布局，并提供了完整的功能实现和用户交互体验。
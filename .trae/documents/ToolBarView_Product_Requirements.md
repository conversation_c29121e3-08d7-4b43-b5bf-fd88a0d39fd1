# ToolBarView 产品需求文档

## 1. 产品概述

基于 Figma 设计稿 (node-id: 2002-2760) 实现一个工具栏视图组件，作为 NewViewController 的扩展方法 `@objc func ToolBarView() -> UIView`。该工具栏将提供图像编辑和处理相关的核心功能按钮，支持用户进行撤销、重做、图层管理等操作。

## 2. 核心功能

### 2.1 用户角色

本组件面向单一用户角色：

* **应用用户**：使用图像编辑功能的终端用户

### 2.2 功能模块

工具栏包含以下核心模块：

1. **撤销/重做区域**：提供操作历史管理功能
2. **图层管理区域**：提供图层相关操作
3. **进度控制区域**：提供处理进度显示和控制
4. **导出功能区域**：提供结果导出功能

### 2.3 组件详情

| 组件名称        | 模块名称 | 功能描述                        |
| ----------- | ---- | --------------------------- |
| ToolBarView | 撤销按钮 | 点击撤销上一步操作，使用 undo\_icon 图标  |
| ToolBarView | 重做按钮 | 点击重做已撤销的操作，使用 redo\_icon 图标 |
| ToolBarView | 图层按钮 | 管理图层显示和编辑，使用系统图标或自定义图标      |
| ToolBarView | 进度滑块 | 显示和控制处理进度，支持用户交互            |
| ToolBarView | 导出按钮 | 导出处理结果，使用 download\_icon 图标 |

## 3. 核心流程

用户操作流程：

1. 用户进入图像编辑界面
2. 工具栏自动显示在界面中
3. 用户可以点击撤销/重做按钮管理操作历史
4. 用户可以通过图层按钮管理图层
5. 用户可以通过进度滑块查看和控制处理进度
6. 用户可以点击导出按钮保存结果

```mermaid
graph TD
    A[进入编辑界面] --> B[显示工具栏]
    B --> C[撤销/重做操作]
    B --> D[图层管理]
    B --> E[进度控制]
    B --> F[导出结果]
    C --> G[更新界面状态]
    D --> G
    E --> G
    F --> H[保存/分享]
```

## 4. 用户界面设计

### 4.1 设计风格

* **主色调**：系统蓝色 (#007AFF) 和中性灰色 (#8E8E93)

* **按钮样式**：圆角矩形，支持高亮和禁用状态

* **字体**：系统默认字体，按钮图标使用 SF Symbols 或自定义图标

* **布局风格**：水平线性布局，按钮间距均匀

* **图标风格**：简洁的线性图标，支持深色模式适配

### 4.2 组件设计概览

| 组件名称        | 模块名称 | UI 元素                             |
| ----------- | ---- | --------------------------------- |
| ToolBarView | 容器视图 | 背景色：systemBackground，圆角：8pt，阴影：轻微 |
| ToolBarView | 撤销按钮 | 44x44pt，图标：undo\_icon，颜色：系统蓝色     |
| ToolBarView | 重做按钮 | 44x44pt，图标：redo\_icon，颜色：系统蓝色     |
| ToolBarView | 图层按钮 | 44x44pt，图标：layers.fill，颜色：系统蓝色    |
| ToolBarView | 进度滑块 | 高度：31pt，最小宽度：120pt，主题色：系统蓝色       |
| ToolBarView | 导出按钮 | 44x44pt，图标：download\_icon，颜色：系统蓝色 |

### 4.3 响应式设计

* **适配方式**：支持横屏和竖屏模式

* **触摸优化**：所有按钮支持触摸反馈和无障碍访问

* **深色模式**：自动适配系统深色模式，图标和颜色相应调整


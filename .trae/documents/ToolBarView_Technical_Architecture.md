# ToolBarView 技术架构文档

## 1. 架构设计

```mermaid
graph TD
    A[NewViewController] --> B[ToolBarView Extension]
    B --> C[UIView Container]
    C --> D[UIStackView Layout]
    D --> E[Undo Button]
    D --> F[Redo Button]
    D --> G[Layer Button]
    D --> H[Progress Slider]
    D --> I[Export Button]
    
    subgraph "UI Components"
        E
        F
        G
        H
        I
    end
    
    subgraph "Assets"
        J[undo_icon]
        K[redo_icon]
        L[download_icon]
    end
    
    E --> J
    F --> K
    I --> L
```

## 2. 技术描述

* **前端框架**：UIKit (iOS Native)

* **布局库**：SnapKit (Auto Layout DSL)

* **图标资源**：Assets.xcassets 中的自定义图标

* **设计模式**：Extension + Factory Method

## 3. 组件定义

### 3.1 核心方法签名

```swift
extension NewViewController {
    @objc func ToolBarView() -> UIView
}
```

### 3.2 内部组件结构

| 组件类型        | 属性名称           | 用途     |
| ----------- | -------------- | ------ |
| UIView      | containerView  | 工具栏主容器 |
| UIStackView | stackView      | 水平布局容器 |
| UIButton    | undoButton     | 撤销操作按钮 |
| UIButton    | redoButton     | 重做操作按钮 |
| UIButton    | layerButton    | 图层管理按钮 |
| UISlider    | progressSlider | 进度控制滑块 |
| UIButton    | exportButton   | 导出功能按钮 |

## 4. 实现规范

### 4.1 布局约束

```swift
// 容器视图约束
containerView.snp.makeConstraints { make in
    make.height.equalTo(60)
    make.leading.trailing.equalToSuperview().inset(16)
}

// StackView 约束
stackView.snp.makeConstraints { make in
    make.edges.equalToSuperview().inset(8)
}

// 按钮尺寸约束
button.snp.makeConstraints { make in
    make.width.height.equalTo(44)
}

// 滑块约束
progressSlider.snp.makeConstraints { make in
    make.width.greaterThanOrEqualTo(120)
    make.height.equalTo(31)
}
```

### 4.2 样式配置

```swift
// 容器样式
containerView.backgroundColor = UIColor.systemBackground
containerView.layer.cornerRadius = 8
containerView.layer.shadowColor = UIColor.black.cgColor
containerView.layer.shadowOpacity = 0.1
containerView.layer.shadowOffset = CGSize(width: 0, height: 2)
containerView.layer.shadowRadius = 4

// 按钮样式
button.tintColor = UIColor.systemBlue
button.backgroundColor = UIColor.clear
button.layer.cornerRadius = 8

// 滑块样式
progressSlider.minimumTrackTintColor = UIColor.systemBlue
progressSlider.maximumTrackTintColor = UIColor.systemGray4
progressSlider.thumbTintColor = UIColor.systemBlue
```

### 4.3 图标配置

```swift
// 使用 Assets 中的自定义图标
undoButton.setImage(UIImage(named: "undo_icon"), for: .normal)
redoButton.setImage(UIImage(named: "redo_icon"), for: .normal)
exportButton.setImage(UIImage(named: "download_icon"), for: .normal)

// 使用系统图标
layerButton.setImage(UIImage(systemName: "square.3.layers.3d"), for: .normal)
```

## 5. 代码实现模板

### 5.1 完整实现结构

```swift
extension NewViewController {
    @objc func ToolBarView() -> UIView {
        let containerView = UIView()
        let stackView = UIStackView()
        
        // 配置容器
        setupToolBarContainer(containerView)
        
        // 配置 StackView
        setupStackView(stackView)
        
        // 创建按钮
        let undoButton = createToolButton(imageName: "undo_icon", action: #selector(undoAction))
        let redoButton = createToolButton(imageName: "redo_icon", action: #selector(redoAction))
        let layerButton = createToolButton(systemName: "square.3.layers.3d", action: #selector(layerAction))
        let exportButton = createToolButton(imageName: "download_icon", action: #selector(exportAction))
        
        // 创建滑块
        let progressSlider = createProgressSlider()
        
        // 添加到 StackView
        [undoButton, redoButton, layerButton, progressSlider, exportButton].forEach {
            stackView.addArrangedSubview($0)
        }
        
        // 添加约束
        containerView.addSubview(stackView)
        setupConstraints(containerView: containerView, stackView: stackView)
        
        return containerView
    }
    
    // MARK: - Helper Methods
    private func setupToolBarContainer(_ container: UIView) { /* 实现 */ }
    private func setupStackView(_ stackView: UIStackView) { /* 实现 */ }
    private func createToolButton(imageName: String? = nil, systemName: String? = nil, action: Selector) -> UIButton { /* 实现 */ }
    private func createProgressSlider() -> UISlider { /* 实现 */ }
    private func setupConstraints(containerView: UIView, stackView: UIStackView) { /* 实现 */ }
    
    // MARK: - Actions
    @objc private func undoAction() { /* 实现 */ }
    @objc private func redoAction() { /* 实现 */ }
    @objc private func layerAction() { /* 实现 */ }
    @objc private func exportAction() { /* 实现 */ }
    @objc private func progressChanged(_ slider: UISlider) { /* 实现 */ }
}
```

## 6. 深色模式支持

### 6.1 颜色适配

```swift
// 在 traitCollectionDidChange 中更新
private func updateToolBarColors() {
    containerView.backgroundColor = UIColor.systemBackground
    containerView.layer.shadowColor = UIColor.label.withAlphaComponent(0.1).cgColor
    
    // 按钮颜色自动适配系统主题
    buttons.forEach { button in
        button.tintColor = UIColor.systemBlue
    }
}
```

## 7. 无障碍支持

### 7.1 可访问性配置

```swift
// 为每个按钮设置无障碍标签
undoButton.accessibilityLabel = "撤销"
undoButton.accessibilityHint = "撤销上一步操作"

redoButton.accessibilityLabel = "重做"
redoButton.accessibilityHint = "重做已撤销的操作"

layerButton.accessibilityLabel = "图层"
layerButton.accessibilityHint = "管理图层显示"

progressSlider.accessibilityLabel = "进度"
progressSlider.accessibilityHint = "调整处理进度"

exportButton.accessibilityLabel = "导出"
exportButton.accessibilityHint = "导出处理结果"
```


# ToolBarView 实现指南

## 1. 实现概述

本文档提供了基于 Figma 设计稿实现 `NewViewController` 扩展中 `ToolBarView()` 方法的完整代码实现。该工具栏包含撤销、重做、图层管理、进度控制和导出功能。

## 2. 完整代码实现

### 2.1 主要实现代码

```swift
extension NewViewController {
    @objc func ToolBarView() -> UIView {
        let containerView = UIView()
        let stackView = UIStackView()
        
        // 配置容器视图
        setupToolBarContainer(containerView)
        
        // 配置 StackView
        setupStackView(stackView)
        
        // 创建工具按钮
        let undoButton = createToolButton(
            imageName: "undo_icon",
            action: #selector(undoAction)
        )
        
        let redoButton = createToolButton(
            imageName: "redo_icon",
            action: #selector(redoAction)
        )
        
        let layerButton = createToolButton(
            systemName: "square.3.layers.3d",
            action: #selector(layerAction)
        )
        
        let exportButton = createToolButton(
            imageName: "download_icon",
            action: #selector(exportAction)
        )
        
        // 创建进度滑块
        let progressSlider = createProgressSlider()
        
        // 添加分隔符
        let separator1 = createSeparator()
        let separator2 = createSeparator()
        
        // 组装 StackView
        let arrangedSubviews: [UIView] = [
            undoButton,
            redoButton,
            separator1,
            layerButton,
            separator2,
            progressSlider,
            exportButton
        ]
        
        arrangedSubviews.forEach { stackView.addArrangedSubview($0) }
        
        // 设置约束
        containerView.addSubview(stackView)
        setupToolBarConstraints(containerView: containerView, stackView: stackView)
        
        // 配置无障碍
        setupAccessibility(for: [
            (undoButton, "撤销", "撤销上一步操作"),
            (redoButton, "重做", "重做已撤销的操作"),
            (layerButton, "图层", "管理图层显示"),
            (progressSlider, "进度", "调整处理进度"),
            (exportButton, "导出", "导出处理结果")
        ])
        
        return containerView
    }
}
```

### 2.2 辅助方法实现

```swift
// MARK: - ToolBar Helper Methods
extension NewViewController {
    
    private func setupToolBarContainer(_ container: UIView) {
        container.backgroundColor = UIColor.systemBackground
        container.layer.cornerRadius = 12
        container.layer.shadowColor = UIColor.black.cgColor
        container.layer.shadowOpacity = 0.08
        container.layer.shadowOffset = CGSize(width: 0, height: 2)
        container.layer.shadowRadius = 8
        
        // 边框（可选）
        container.layer.borderWidth = 0.5
        container.layer.borderColor = UIColor.separator.cgColor
    }
    
    private func setupStackView(_ stackView: UIStackView) {
        stackView.axis = .horizontal
        stackView.distribution = .fill
        stackView.alignment = .center
        stackView.spacing = 12
    }
    
    private func createToolButton(
        imageName: String? = nil,
        systemName: String? = nil,
        action: Selector
    ) -> UIButton {
        let button = UIButton(type: .system)
        
        // 设置图标
        if let imageName = imageName {
            button.setImage(UIImage(named: imageName), for: .normal)
        } else if let systemName = systemName {
            button.setImage(UIImage(systemName: systemName), for: .normal)
        }
        
        // 样式配置
        button.tintColor = UIColor.systemBlue
        button.backgroundColor = UIColor.clear
        button.layer.cornerRadius = 8
        
        // 高亮效果
        button.layer.masksToBounds = true
        button.adjustsImageWhenHighlighted = true
        
        // 添加触摸反馈
        button.addTarget(self, action: action, for: .touchUpInside)
        
        // 设置约束
        button.snp.makeConstraints { make in
            make.width.height.equalTo(44)
        }
        
        return button
    }
    
    private func createProgressSlider() -> UISlider {
        let slider = UISlider()
        
        // 样式配置
        slider.minimumValue = 0.0
        slider.maximumValue = 1.0
        slider.value = 0.5
        
        slider.minimumTrackTintColor = UIColor.systemBlue
        slider.maximumTrackTintColor = UIColor.systemGray4
        slider.thumbTintColor = UIColor.systemBlue
        
        // 添加事件
        slider.addTarget(self, action: #selector(progressChanged(_:)), for: .valueChanged)
        
        // 设置约束
        slider.snp.makeConstraints { make in
            make.width.greaterThanOrEqualTo(120)
            make.height.equalTo(31)
        }
        
        return slider
    }
    
    private func createSeparator() -> UIView {
        let separator = UIView()
        separator.backgroundColor = UIColor.separator
        
        separator.snp.makeConstraints { make in
            make.width.equalTo(1)
            make.height.equalTo(24)
        }
        
        return separator
    }
    
    private func setupToolBarConstraints(
        containerView: UIView,
        stackView: UIStackView
    ) {
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 8, left: 16, bottom: 8, right: 16))
        }
        
        containerView.snp.makeConstraints { make in
            make.height.equalTo(60)
        }
    }
    
    private func setupAccessibility(for items: [(UIView, String, String)]) {
        items.forEach { view, label, hint in
            view.isAccessibilityElement = true
            view.accessibilityLabel = label
            view.accessibilityHint = hint
            view.accessibilityTraits = view is UIButton ? .button : .adjustable
        }
    }
}
```

### 2.3 事件处理方法

```swift
// MARK: - ToolBar Actions
extension NewViewController {
    
    @objc private func undoAction() {
        print("Undo action triggered")
        // TODO: 实现撤销逻辑
        // 示例：
        // if canUndo {
        //     performUndo()
        //     updateUndoRedoButtonStates()
        // }
    }
    
    @objc private func redoAction() {
        print("Redo action triggered")
        // TODO: 实现重做逻辑
        // 示例：
        // if canRedo {
        //     performRedo()
        //     updateUndoRedoButtonStates()
        // }
    }
    
    @objc private func layerAction() {
        print("Layer action triggered")
        // TODO: 实现图层管理逻辑
        // 示例：
        // presentLayerManagementViewController()
    }
    
    @objc private func exportAction() {
        print("Export action triggered")
        // TODO: 实现导出逻辑
        // 示例：
        // presentExportOptionsViewController()
    }
    
    @objc private func progressChanged(_ slider: UISlider) {
        print("Progress changed to: \(slider.value)")
        // TODO: 实现进度变化逻辑
        // 示例：
        // updateProcessingProgress(slider.value)
    }
}
```

## 3. 集成步骤

### 3.1 在现有视图中使用

```swift
// 在 NewViewController 的 setupUI 方法中添加
private func setupUI() {
    // ... 现有代码 ...
    
    // 添加工具栏
    let toolBar = ToolBarView()
    view.addSubview(toolBar)
    
    // 设置工具栏约束
    toolBar.snp.makeConstraints { make in
        make.leading.trailing.equalToSuperview().inset(16)
        make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-16)
    }
}
```

### 3.2 深色模式适配

```swift
// 在现有的 updateColorsForCurrentTraitCollection 方法中添加
private func updateColorsForCurrentTraitCollection() {
    // ... 现有代码 ...
    
    // 更新工具栏颜色
    if let toolBar = view.subviews.first(where: { $0.accessibilityIdentifier == "ToolBar" }) {
        toolBar.backgroundColor = UIColor.systemBackground
        toolBar.layer.shadowColor = UIColor.label.withAlphaComponent(0.1).cgColor
        toolBar.layer.borderColor = UIColor.separator.cgColor
    }
}
```

## 4. 测试和验证

### 4.1 功能测试清单

* [ ] 工具栏正确显示所有按钮和滑块

* [ ] 撤销按钮点击响应正常

* [ ] 重做按钮点击响应正常

* [ ] 图层按钮点击响应正常

* [ ] 进度滑块拖拽响应正常

* [ ] 导出按钮点击响应正常

* [ ] 深色模式切换正常

* [ ] 无障碍功能正常

* [ ] 横屏适配正常

### 4.2 样式验证

* [ ] 工具栏圆角和阴影效果正确

* [ ] 按钮图标显示清晰

* [ ] 分隔符位置和样式正确

* [ ] 整体布局符合设计稿

* [ ] 触摸反馈效果良好

## 5. 性能优化建议

### 5.1 图标优化

```swift
// 预加载图标以提高性能
private func preloadToolBarIcons() {
    let iconNames = ["undo_icon", "redo_icon", "download_icon"]
    iconNames.forEach { iconName in
        _ = UIImage(named: iconName)
    }
}
```

### 5.2 内存管理

```swift
// 使用弱引用避免循环引用
private weak var currentToolBar: UIView?

@objc func ToolBarView() -> UIView {
    let toolBar = createToolBar()
    currentToolBar = toolBar
    return toolBar
}
```

## 6. 扩展功能

### 6.1 动画效果

```swift
// 添加按钮点击动画
private func animateButtonPress(_ button: UIButton) {
    UIView.animate(withDuration: 0.1, animations: {
        button.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
    }) { _ in
        UIView.animate(withDuration: 0.1) {
            button.transform = .identity
        }
    }
}
```

### 6.2 状态管理

```swift
// 按钮状态管理
private func updateButtonStates() {
    undoButton.isEnabled = canUndo
    redoButton.isEnabled = canRedo
    undoButton.alpha = canUndo ? 1.0 : 0.5
    redoButton.alpha = canRedo ? 1.0 : 0.5
}
```


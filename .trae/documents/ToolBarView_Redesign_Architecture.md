# ToolBarView 重新设计技术架构文档

## 1. 架构设计

```mermaid
graph TD
    A[NewViewController] --> B[ToolBarView Extension]
    B --> C[Container View]
    C --> D[Cards Container]
    C --> E[Progress Container]
    
    D --> F[History Card]
    D --> G[Layer Card]
    D --> H[Export Card]
    
    E --> I[Progress Bar]
    
    subgraph "Card Components"
        F --> F1[Undo/Redo Icons]
        F --> F2[History Label]
        G --> G1[Layer Icon]
        G --> G2[Layer Label]
        H --> H1[Export Icon]
        H --> H2[Export Label]
    end
    
    subgraph "Assets"
        J[undo_icon]
        K[redo_icon]
        L[download_icon]
        M[layers_icon]
    end
    
    F1 --> J
    F1 --> K
    G1 --> M
    H1 --> L
```

## 2. 技术栈

* **框架**: UIKit (iOS Native)

* **布局**: SnapKit (Auto Layout DSL)

* **动画**: Core Animation

* **主题**: 深色模式原生支持

* **图标**: SF Symbols + 自定义图标

## 3. 组件架构

### 3.1 核心方法签名

```swift
extension NewViewController {
    @objc func ToolBarView() -> UIView
}
```

### 3.2 组件层级结构

| 层级 | 组件类型           | 属性名称              | 用途     |
| -- | -------------- | ----------------- | ------ |
| 1  | UIView         | containerView     | 工具栏主容器 |
| 2  | UIStackView    | cardsStackView    | 功能卡片容器 |
| 2  | UIView         | progressContainer | 进度条容器  |
| 3  | UIView         | historyCard       | 操作历史卡片 |
| 3  | UIView         | layerCard         | 图层管理卡片 |
| 3  | UIView         | exportCard        | 导出分享卡片 |
| 3  | UIProgressView | progressBar       | 进度指示器  |

### 3.3 卡片内部结构

每个功能卡片包含：

```swift
struct CardComponents {
    let containerView: UIView
    let iconImageView: UIImageView
    let titleLabel: UILabel
    let tapGesture: UITapGestureRecognizer
}
```

## 4. 设计规范实现

### 4.1 颜色定义

```swift
struct ToolBarColors {
    static let background = UIColor(red: 0.17, green: 0.17, blue: 0.18, alpha: 1.0) // #2C2C2E
    static let cardBackground = UIColor(red: 0.28, green: 0.28, blue: 0.29, alpha: 1.0) // #48484A
    static let iconColor = UIColor.white
    static let textColor = UIColor.white
    static let progressColor = UIColor.white
}
```

### 4.2 尺寸规范

```swift
struct ToolBarMetrics {
    static let containerHeight: CGFloat = 120
    static let cardSize = CGSize(width: 80, height: 80)
    static let cardCornerRadius: CGFloat = 12
    static let cardSpacing: CGFloat = 16
    static let containerPadding: CGFloat = 16
    static let progressHeight: CGFloat = 4
    static let progressMargin: CGFloat = 20
}
```

### 4.3 字体规范

```swift
struct ToolBarFonts {
    static let cardTitle = UIFont.systemFont(ofSize: 12, weight: .medium)
    static let iconSize: CGFloat = 24
}
```

## 5. 实现细节

### 5.1 主要实现方法

```swift
extension NewViewController {
    @objc func ToolBarView() -> UIView {
        let containerView = createContainerView()
        let cardsStackView = createCardsStackView()
        let progressContainer = createProgressContainer()
        
        // 创建功能卡片
        let historyCard = createHistoryCard()
        let layerCard = createLayerCard()
        let exportCard = createExportCard()
        
        // 组装视图
        [historyCard, layerCard, exportCard].forEach {
            cardsStackView.addArrangedSubview($0)
        }
        
        containerView.addSubview(cardsStackView)
        containerView.addSubview(progressContainer)
        
        setupConstraints(containerView, cardsStackView, progressContainer)
        
        return containerView
    }
}
```

### 5.2 卡片创建方法

```swift
private func createFunctionCard(
    iconName: String?,
    systemIconName: String?,
    title: String,
    action: Selector
) -> UIView {
    let cardView = UIView()
    let iconImageView = UIImageView()
    let titleLabel = UILabel()
    
    // 配置卡片样式
    cardView.backgroundColor = ToolBarColors.cardBackground
    cardView.layer.cornerRadius = ToolBarMetrics.cardCornerRadius
    
    // 配置图标
    if let iconName = iconName {
        iconImageView.image = UIImage(named: iconName)
    } else if let systemIconName = systemIconName {
        iconImageView.image = UIImage(systemName: systemIconName)
    }
    iconImageView.tintColor = ToolBarColors.iconColor
    iconImageView.contentMode = .scaleAspectFit
    
    // 配置标签
    titleLabel.text = title
    titleLabel.font = ToolBarFonts.cardTitle
    titleLabel.textColor = ToolBarColors.textColor
    titleLabel.textAlignment = .center
    
    // 添加点击手势
    let tapGesture = UITapGestureRecognizer(target: self, action: action)
    cardView.addGestureRecognizer(tapGesture)
    
    // 布局
    cardView.addSubview(iconImageView)
    cardView.addSubview(titleLabel)
    
    setupCardConstraints(cardView, iconImageView, titleLabel)
    
    return cardView
}
```

### 5.3 约束设置

```swift
private func setupConstraints(
    _ container: UIView,
    _ cardsStack: UIStackView,
    _ progressContainer: UIView
) {
    // 容器约束
    container.snp.makeConstraints { make in
        make.height.equalTo(ToolBarMetrics.containerHeight)
    }
    
    // 卡片容器约束
    cardsStack.snp.makeConstraints { make in
        make.top.equalToSuperview().offset(ToolBarMetrics.containerPadding)
        make.centerX.equalToSuperview()
        make.height.equalTo(ToolBarMetrics.cardSize.height)
    }
    
    // 进度条容器约束
    progressContainer.snp.makeConstraints { make in
        make.bottom.equalToSuperview().offset(-ToolBarMetrics.containerPadding)
        make.leading.trailing.equalToSuperview().inset(ToolBarMetrics.progressMargin)
        make.height.equalTo(ToolBarMetrics.progressHeight)
    }
}

private func setupCardConstraints(
    _ card: UIView,
    _ icon: UIImageView,
    _ label: UILabel
) {
    // 卡片尺寸
    card.snp.makeConstraints { make in
        make.size.equalTo(ToolBarMetrics.cardSize)
    }
    
    // 图标约束
    icon.snp.makeConstraints { make in
        make.centerX.equalToSuperview()
        make.top.equalToSuperview().offset(16)
        make.size.equalTo(ToolBarFonts.iconSize)
    }
    
    // 标签约束
    label.snp.makeConstraints { make in
        make.centerX.equalToSuperview()
        make.bottom.equalToSuperview().offset(-12)
        make.leading.trailing.equalToSuperview().inset(8)
    }
}
```

## 6. 动画效果

### 6.1 卡片点击动画

```swift
private func animateCardPress(_ cardView: UIView) {
    UIView.animate(withDuration: 0.1, animations: {
        cardView.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        cardView.alpha = 0.8
    }) { _ in
        UIView.animate(withDuration: 0.1) {
            cardView.transform = .identity
            cardView.alpha = 1.0
        }
    }
}
```

### 6.2 进度条动画

```swift
private func updateProgress(_ progress: Float, animated: Bool = true) {
    if animated {
        UIView.animate(withDuration: 0.3) {
            self.progressBar.setProgress(progress, animated: true)
        }
    } else {
        progressBar.setProgress(progress, animated: false)
    }
}
```

## 7. 事件处理

### 7.1 卡片点击事件

```swift
// MARK: - Card Actions
@objc private func historyCardTapped() {
    animateCardPress(historyCard)
    // 处理操作历史逻辑
    presentHistoryOptions()
}

@objc private func layerCardTapped() {
    animateCardPress(layerCard)
    // 处理图层管理逻辑
    presentLayerManagement()
}

@objc private func exportCardTapped() {
    animateCardPress(exportCard)
    // 处理导出分享逻辑
    presentExportOptions()
}
```

## 8. 深色模式支持

### 8.1 主题适配

```swift
override func traitCollectionDidChange(_ previousTraitCollection: UITraitCollection?) {
    super.traitCollectionDidChange(previousTraitCollection)
    
    if traitCollection.hasDifferentColorAppearance(comparedTo: previousTraitCollection) {
        updateToolBarColors()
    }
}

private func updateToolBarColors() {
    // 深色主题下颜色保持不变，因为设计本身就是深色主题
    containerView.backgroundColor = ToolBarColors.background
    
    [historyCard, layerCard, exportCard].forEach { card in
        card.backgroundColor = ToolBarColors.cardBackground
    }
}
```

## 9. 无障碍支持

### 9.1 可访问性配置

```swift
private func setupAccessibility() {
    // 操作历史卡片
    historyCard.isAccessibilityElement = true
    historyCard.accessibilityLabel = "操作历史"
    historyCard.accessibilityHint = "管理撤销和重做操作"
    historyCard.accessibilityTraits = .button
    
    // 图层管理卡片
    layerCard.isAccessibilityElement = true
    layerCard.accessibilityLabel = "图层管理"
    layerCard.accessibilityHint = "管理图层显示和编辑"
    layerCard.accessibilityTraits = .button
    
    // 导出分享卡片
    exportCard.isAccessibilityElement = true
    exportCard.accessibilityLabel = "导出分享"
    exportCard.accessibilityHint = "导出和分享处理结果"
    exportCard.accessibilityTraits = .button
    
    // 进度条
    progressBar.isAccessibilityElement = true
    progressBar.accessibilityLabel = "处理进度"
    progressBar.accessibilityTraits = .updatesFrequently
}
```

## 10. 性能优化

### 10.1 视图复用

```swift
private var toolBarCache: UIView?

@objc func ToolBarView() -> UIView {
    if let cachedToolBar = toolBarCache {
        return cachedToolBar
    }
    
    let toolBar = createToolBar()
    toolBarCache = toolBar
    return toolBar
}
```

### 10.2 图标预加载

```swift
private func preloadIcons() {
    let iconNames = ["undo_icon", "redo_icon", "download_icon"]
    iconNames.forEach { iconName in
        _ = UIImage(named: iconName)
    }
}
```


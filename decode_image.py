import base64
import sys

# 从 MCP 获取的 base64 数据
base64_data = """iVBORw0KGgoAAAANSUhEUgAAAXcAAAMsCAYAAACvFuLjAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUABYBASURBVHgB7P1nkGVbdh6IrWOuN+mzsryv5333637dDaANHEWCJACSmhkOCAKYERXBUcwAUIRGitBoCEoTIVF/CEgKURHUCKA4I41EEQRJ0QAE0K9h2r5+rp+tV74qK729/t5j5vvW3vvck9XeN2rOfp2dWZn3nnvONst861treSKSSjGKUYxiFOOhGr4UoxjFKEYxHrpRCPdiFKMYxXgIRyHci1GMYhTjIRyFcC9GMYpRjIdwFMK9GMUoRjEewlEI92IUoxjFeAhHIdyLUYxiFOMhHIVwL0YxilGMh3AUwr0YxShGMR7CUQj3YhSjGMV4CEch3ItRjGIU4yEchXAvRjGKUYyHcBTCvRjFKEYxHsJRCPdiFKMYxXgIRyHci1GMYhTjIRyFcC9GMYpRjIdwhPIDNP7O3/k78vGPf1xmZ2fl1q1b8nf/7t+VV1999Rt+/7lz5/QaHP/8n/9z+Z3f+Z0jf+e1f+EXfkH29/flV3/1V6UYxSjGwzWc/KDcoAz5WoOve/bZZ+Wnf/qn9Wc3XnvtNXnxxRe/Ydlz/vx5WV5elu/E2NzclJs3b8p3YnjyA9KJ6Td/8zflF3/xF1XwclK5SBz83T/6R//oG7oGJ4UCnoOK4dd+7deyv/39v//35Vd+5Vf0Zy46F6QYxSjGwzN+5md+Rv7ZP/tn+vNv/dZvyS/90i99xddRkP/yL/+yyoO8UH9wOAOT1/pq48knn5SnnnpKvpPj5ZdflnfffVe+3fEDYblzUSjEKdQ/8YlPqICnkH7llVfk13/919UK5+++1qDFzvdwIXit/OCC8zN+4zd+Qxe1GMUoxsM3vpagdoNGI+VB/rWUO85Kd9Y8ZQm/aHTS26ei+EqewKOPPqrfP/rRj8pjjz0m38744he/KC+99JIqjO+EcOdIv99fsLBTDgjgI7+HYNffQ1jrvzHZKSb7y17H33Hwb1g8/ZnXdH/HYurv+zMXX8VX8fXtffEc588z5YQblAUPvt7JCY69vT2VERDmX/HaEPApDMXs9TA0v+Lr/vpf/+v69Z0YvV4vu953Yn5+oAKqDlJxY2ZmRr9Tk3LQIueXw9XdoCZ2LtRXGj/7sz+rGFoxilGMh2c4efGg3Phqw8kIfn/uuecUtqVsoXVOSJdfn/zkJxXCJVJAWeIs9q8G8XwnR71eP/LvD3/4w/LtjB8IWIaClwKbkAknki4SXSEHrzghT8jFBVvd4Gu4QG4RvtGFLkYxivFnZ/CMUz5Q6FJAfz2Y9qsNCnRCvZQjFOQutucG5Qd/Rzyer/16mPt3cxDLp0xbW1uTb2X8wAh3TiIFvAuIOEYLtejBwYH+jg/qgqIcjh3D33+/FqAYxSjGd3/w3NPg4yCb5ds5784qd4Kd8sfF9Rx7hrKFn0kyx9dj3fygjh8YKiS1JBeMk0utShojg6AcX21yuTguiOqYMWfPntXvXCD+/L1wp4pRjGJ850ae0kwSxDdDh/5Gh7PeeW1Hmea/KUuc1c7ffz8FO4Oq36rVzvEDIdwd//xTn/rUEY3smC95vjoX3k24g2AeZMdwUEk4uKYYxSjGn53Bc5s/09+tM+wMQioSR4ukbCFikKdRfyOj3+9/GWb+7Q7Kw29n/EAId2pPwjFc0GeeeUbdLlreXGQKeyfM+XcGP+hGkTLJBXhwEagoiKU9yHMvRjGK8WdjfCOUxu/k8Dwv+8wHY3pfb0wmEymVSvIP/sE/kA984APy7Yy33npLv4/HY/lOjB8I4U6si9FrCuU8pk6LPZ9J6oIo32owpRjFKMYP/sgLVxdvy4+8HMgL46/3vq828hY8jcZvBgZ6/fXX5f3vf79y1Pn1nRhXr16V78T4gclQdcNh7pzgryTE+bdCuBejGA/3YLzNxd5cUiNlA392tGbHbMknIT34vm9m8PrfCr5flB8oRjGKUYxifM9GURWyGMUoRjEewlEI92IUoxjFeAhHIdyLUYxiFOMhHIVwL0YxilGMh3AUwr0YxShGMR7CUQj3YhSjGMV4CEch3ItRjGIU4yEchXAvRjGKUYyHcBTCvRjFKEYxHsJRCPdiFKMYxXgIRyHci1GMYhTjIRyFcC9GMYpRjIdwFMK9GMUoRjEewlEI92IUoxjFeAhHIdyLUYxiFOMhHIVwL0YxilGMh3AUwr0YxShGMR7CUQj3YhSjGMV4CEch3ItRjGIU4yEchXAvRjGKUYyHcBTCvRjFKEYxHsJRCPdiFKMYxXgIRyjFKEYxivEDNGqtj0tj/qf1ux/MSoAvP5yVh31MRrckwtdo8Krsr/2GRONb8u0MD1+pFKMYxSjG93lQkM+f/Dsyu/IrUgyR/fVfl93VvytJvC/fyiiEezGKUYzv+6BgP/nYJ6VSf1aKMR2j/quy+vYnviUBX2DuxShGMb7vgxZ7Idi/fHBOODffyigs92IUoxjf1xGWz8m5Z29KMb76oPU+6Lz4zbylsNyLUYxifH9HgbF//dGY+xn5Zsf3TLh//OMfl1/5lV+R2dmHP+pdjGIU4xsflcYzUoyvPWqtj8k3O74nVMhf+IVfkN/6rd/Sn5955hn5pV/6JSlGMYpRDA7CMsX42iP4Fqig33XLPS/Yi1GMYhTjwVGqnJNifO3h/6AJ9wcF+6uvviq/+qu/KsUoRjGK8d0cj10WOXlcHppBqug3O75rsMxXEuyf+MQnZH//WyPkF6MYxSjGNzp+8T8Q5QL+L/+33/h7Wi2Rxy9/7dccdEXeufrV/06l8ot/XeT/9A9FVtfk+zq+K8K9EOzFKEYx/qwNCvb/5h987df89r86qjDaUAj/1/+jyH/1343Q57//yk/hdf9/I9wp7P/X/3OR/xXec+97LOy/48K9EOzFKEYx/iyPv/G3v7LV/X/4L7/8d62myAvvE5lpylccFPb8+/djfEeFeyHYi1GMYvxZH1/Leqfl/mdlfMeE+8/8zM8Ugr0YxSjG920QDjl5QuT//A+P/p74O9Pw/9F/941d56tZ7hzE3Cn8Cc3kX/PoFfP9sStf+d/fj/EdE+6//Mu/nP1cCPZiFKMY3+vxNjDv/+IBMt5/+rdEfunfF/m5v/313+8w8b/3X331QB+xdGJpEBj84Of+Fz8ApMCinnsxilGMh2K8/Z4R4v/tA7AKf8e/fb1BS/w/+c8Njv7gIK2Sgp0KhK9zVju/X35h+jri67Tsaf1/7mX5vo7vmHD/jd/4DS0xwPHss8/KJz/5ycJ6L0YxivFdH4Q+3rb0xAcF/IOCPf/a/Pjxj4n8xNfI8HdBUQp4DtImf/9TRpB/7ouAgv7vX/29/+n/xLz/b/wn8j0d37Ekpt/5nd85UlbACfiilkwxilGM79agRf0v/vHR31GY/6WfF/nYz365xc7XnvoKyU3tprkWvx6/Mv3ZfT34mvZXYcf8II3vKCzjAqq/+Zu/qd8LC74YxSjG92N8swlEZMHwi5Y5FcDd+wia/u9EOh3z97/3v5FvOinq+z2+45h7IeCLUYxifK8HremvJ9CdBU7Gy1cbhxDmf/t/YWCdf/D3plAKhf7hV3nfT3wc3sAJ+zpr0f9nf2calCUUdHgo3/PxXQmoFgK+GMUoxvdiUKCT4vji73xjr/+t/25qjX+1QSjnb//nRtATL/8rf9EkKf3Tr8Jxd3CNGy6Qmodzvh/C/bvaiekXf/EXMwHPUVAki1GMYjw4Ln3w2xdBDHDOfAM4+DdbAoAC2gVT/90ffbliUKu887W9BofRfyOMna81rn3e+6Ze/11vs/eggKdVX9RzL0YxiuHGhfftfUslbf+HNr5Z4f5dr+deCPNiFKMYX2vEceHJf70RjW7JNzu+J0lMFPC3bt1S7L1o3FGMYhQjP5JCuH/dMflBFe4cL774on4VoxjFKEZ+DA4/JZX6s1KMrz5G/dfkmx3fswbZxShGMYrxlUZv7xukuvwPeOxv/Lp8s6MQ7sUoRjG+r2PQeVH2139DivGVB+fmW8Hcv+tsmWIUoxjF+HqDPUJPPvbJAp55YIz6r8rqO5+QJPrm4xKF5V6MYhTj+z4YVF19+xOFBZ8bnItvVbBzFJZ7MYpRjB+oEVbOyezKr8CKf0bC8jkp4d8P+6AAJyWUSm7Q+ZT0dn9H4apvZxTCvRjFKEYxHsJRwDLFKEYxivEQjkK4F6MYxSjGQzgK4V6MYhSjGA/hKIR7MYpRjGI8hKMQ7sUoRjGK8RCOQrgXoxjFKMZDOArhXoxiFKMYD+EIP/7nflXSNBXP8/BdJE4iZb77vq9f/BsHf46iSF+XJIn+LokTKZVL2cX4+zAMcY1EYryWP7v3c/D95XJZf8evSTSRcqmcvY/fef3JZIzrhnj9REjF9/0Af+dXSf/Ge5lMJvoeDn4E/z4ajfT6cRyb6+trShIGoYwnQwkC6rJAAq8m0diXheikXLrgy3/8n/2U/P/+8J/I2ccX5ff++b+Ve+8eSlo6LXFak3alJvPlsfzNn/sR+eJn7sv9W+/I7NmSvPTOl2Q4msGHL0uMzxxNBuJhWkqlUJ+zVCrpffB7hO9mTpmsEOPzcQ/4Rxh4Zk6qFRmPx7i/QF/L58IsY34xT5jjEPftxSYpwcd8x7wGfud7WJORWRPf94T/6YvMT/r5ZXw+3iApPmvMecHrJni/z9+n+HcSi8cb8z2dM11bfA9xj7znVH9v1xyfF6eRBFibeIL1LZV0jnVecQ+8Bj+T96N7xjPP52HOp3vMfIY+K+/D93Q9Y96j3Xf6Om5OvIbXM2t8NB3DXY/X4mt4PQ53XbNnvWyvcV9wjDDPPqcJe8rD85e5N8a8ViAJns0LEpl4eMawIqVRVaqTqoR4/r/6H3xcfuTPnZLu/kSSQUlqpZb8yafekD/5/ItyEL0j4/6BeGN8Nu5/5FUkCdpYv7pEaQn3ZO6Be9Tt8VTsUuUG/8bn53f3c5DNgadTnCRmHvgz91r+vLpn5xrwOtybo9FYKhWz39z5dGvC++LreK1arabv49/NPg71s/ke/p7X53njZ7pruc/UPW/f69ZlMol0n7j7dGumexTP4V7v3mPkwPSMZ/Nm7zUvL9x7BoOBVKvVbE74edwLpVKgeytNkyNz6/thtl8pYxKcBfe3/P5y8oXPz2twLfjd7akH740/uzPg7sXcI58lydZQz7k9I+69bg3zz+WuIfZMuzVw391n5d/H6/huQt1hoCB0D8jJzT8oH0pv3jc3X66U9TX80kmyG9O3f+cH54dTEPyiYA/sZ5UyIRFmC8fPqlSqdtPkr+dlgtAJienkBdnm4eu4IXVBU3OUuNBhUMK/ea/ctJzrAO6LL+1WG5/lS7VWlaBkNQaO4BAbmK+rVmty/MQxvad6raHPzoWq1iqqzMo4MO4+3MZzk1/Cd240CnVPrNLUjWKF0Xii4jjhRqcCowDCTfiYj0A3AO4Rm0vvSOfLrhGuwacvca7wFeK5eBc1CPIylquK74GH5yxDeFJ+U0lwo/IexjhsUaqCq0KFh2cMU19qYVkFO5c7xYs9K4KS1ByWKp+TSh33KbwXPhuEvr4qMS/kv6iU+F4qIDMXpWxD68ZTweXbg0DhPhVsdiJ1T/H37rDwdU4gcd6cMuD13EFxyob37za92yvcNyU7n/xLCfPLeeYcx6lRfAkVXYx7jMvi47OruPaP/fgT8vEfvyyjHp49qWKvzMh7727KZ7/4r6U/viWDHoRCzPUJYERw7hpQ3nVVfrVKyd5Hmp0nFRh2fzwoXPPCx51L85pEjR0KIF7LnU9zLmI9O9xrThm6wc9wQohzybmgUFYjzBkSdn75OSoDwlDnyt2Pm2s3v25ejSASVQSZYMHve71edj13D2qMqEA1At0pB6PY45xA9HLPFOizur9nhkNOmDnFzWen0soMzyQ15x2fbe5Zjigctw7cU6k961RA5pnT7LPNfYldl9jOh5fJmAeFKq+XpE4BGyXgDBbz/lTyMtf97BQrr0lDxygVs2d4H3lF6V7v3pN9bk6R6B7iargNFU2ibDGcZuAi8286QRbEoeXoZYc2nFpbidFKSZTYBZhuVmfFGi0JIQJhKfbB+Blms45zWtVsJPeQ5n6M0DGa3/ye2poT6BaeDzsej+z6O4FgFpOCOVGNi4XxaHlWIEg5G4E0qxDYeJa5hZY+pwczPE5GulkPD0cygMVz7uJpCCtYKoNYWvUG3hfJeNiTkEpLrYzp5JtNaw6sHhT8fjgc6hyJVYCZpYINT0FdgiXh4T0VKqBJokKWqidUwc0vkTpeU4FQngvK0sTvF2GpzODgNPFcC/weT6SN68/g3/O4hyY8sQrmro7rV7EuLby/wS88ew3WadWHEEswbxDs/LQE16bg5mGs4NCEJQpA0Q2dWU3cUNiotMw9d1h4jxQMfBbuIbWMRb8q9M64YanYaQhQmSVpzrqJrQBOM6FjDo9k1i6Fgjtk5hAb72UqfIyydIdqKugD+xlJTli5/Wq8x1GMdYZiT/WzoOz8mlSGdWkFdfkLP/W4/JV//3ko4LGM+ok0awvy8kvvyT/7F/9YOr0NOezdhyIbYh8FMqSAD2axZ2Zxvy0V9txnxitJM0vVCRcn5Nwed9/z96nGQim0z+WrIHOGjlNY3M/8jLw1+6AQ4Pyl9qw7ocE5piU/FUyi/3b711n87l7dGXPvN4pkqrQoTPnJ9Xsts3g5zL2m2We4a3AdY3s/qTXAuK7G8gz1/VGcZAqIr+cFKlZZ6dpaoZ+1+o3iCTM54oS/U6b8vXm/MQLcvLi9RnlCxUNhzzPsFI4atJhrZ3lzzp0ynSpVc56dzFJ5mPMkaOS4tTZyMcmtkacK3HgZqVVGic6BU85uX/MZKOfcWaHsnK6jkTk+rXA3IX4wdQl1g8XTg+OsTf7bLLiXHaT8UCubk2etAP6dH84HjuIom9i8++g2nBP+RnvF2We74TQvH2S6iCW7AGaCzYTKkU3OCTOHWeceJ06IxmycSUQICcexPatQkgoMMRZpWDLP7OOwj8YQlsuzeA7APnhPtcrPHeJzsBjc9GIOAN1CHiQjrGKdQyoNCjla5ZKzHNzhFQuFUOCpIBda3oChKPBjfuEa+LmJ67bwMwV5E5ZmAw9TicfSxGVbJU8aEMBztbLUfAhx3HsNCmoOn72E9x6DYFjEPbb0OvhsvJ+anTNEYR3qPWHmA2Nl+tyAWK9yENh7hwUI99bdK9eTEFNov9MKVq+AMAvXjxYHBbg+X2r+Fhgr3611Eudd1gCHt2zht6nlOHXjo0ygUYiYbeFlymB6eKb9Z3gQ3WY3gkkyQWVgQXN4S4Q3FAbDfsfspyPsWXh0H3j/Rfmxn3hSP3N7cyTNRkPee+e2/MGLvycHw2vSGRxAUdGDGanTApARlj/gjJTCxOxjzqWxXKewET8/vz9VaImXeTTOG3HeDI0uI+CMIMoLM74vb4kbpZhmBtXUQJp61O4zEusF8nN4DxQ87gyOx5Pp/rQWqju7eSHvZIMTjlMvy3MGdg5unRzxLBTeyV2H59R5JFx/gwtaS1eMEcBLUgEZoZlmHp4zMJ1Ccoqdz+YUGO/Dwbbu881cJkeu4Z6J3hCfiX/PG2xH4SM/m1M3L0ZAT61354UZBZtk8+/2rPu3W28Hx2XXz805/+aewSgKyWCy6WcZAz10lrLTbHyBsdxju5mm1pTDM/nADmfj77kxuMG4AhQOepiS6UTrRksMtjaCtlFBn8OK3AONqX3Esi802HutVs82hXtA4+KbCTaYXSmHaaXZIaJV7xYnhJVK4W6eAYIHIDZx/UnUl0G3Lw18zs5kU+bmF/EZ13AveB5AFAks2tHEk4NOXyqNAJBMW7rDrn5mpYJNxI3De0kSFez9fj+ziJwQ5+Gk9ZoSxsFc8d81/T4xQtUqKV93sIlyVyhg+Rx8Xh5CtVggYLmxPAqSSEYylgGszqHeAxbdD9XacBu3Aiy/gjmZwbWBzEhNfBVCA0xEDfMySVWM40oQmDgo44SYMzYUrNiYnoOFy+gt8H28b71HKBxfBWWgVrhaDlj/lJvLrmmtbARWbFdE4Rkr1KgQxorXhuZvdv8596MLgoOdR7gXXocWZL8/1M3c7/XVQqDcn2i9D7Eu/0StNVrhCqmVjAvuh0aQcynLsKBZaoEYO7+YaaxwG4NQNoXeWUW0wBgAdcFOd8C0rjv3lWcy8qjEKqw0afdHqin63D98Zgh93P8+9sYhFMB6GMstbyBvR5vy9nBN7ox3pOf1cF8pLPpEZmANzuB+IZu1Kmadik1M+QCuAYU5YSmtpgkrh56MHa+vJE1ZWSrJ3/zFn4SHdgKwygBzNNF7eunlz8sXX/sjGSWbmIYh3lOHdVqVRhMH9NSHpNlekL3+a3Kwv4k5xP37NcBWiHPExlrX9fK9I7hyPoPRJdqZQz2tIOjK9LpzVamUjxhLDoIwRo6l92GR2hAs5xEXaQIWHOF+39i9KT0c8PIgledmzklpQGMj0PPG/3QjeNazcN9zOSp5eCO1AWx3lt2zOIGqNYKwfoPyBBj/qmyNEbwHNPTo7El4DQiEw0B45xACjlh9lGY013cipHs+xx4ScRUX3fNPKyASKvE8k+m8rZviMHvDFnE1rxwxZFr24CjE7OAfZ8y68+WMyIx5Etu8GbtOTgFqHIVHwLf13IdjOVdtyjG93kTW4n25O9rT+...(461053 characters truncated)"""

try:
    # 尝试解码 base64 数据
    image_data = base64.b64decode(base64_data)
    
    # 保存为 PNG 文件
    with open('figma_design_screenshot.png', 'wb') as f:
        f.write(image_data)
    
    print("✅ 图片已成功保存为 figma_design_screenshot.png")
    print(f"📏 图片大小: {len(image_data)} 字节")
    
except Exception as e:
    print(f"❌ 解码失败: {e}")
    print("尝试修复 base64 填充...")
    
    # 修复 base64 填充
    missing_padding = len(base64_data) % 4
    if missing_padding:
        base64_data += '=' * (4 - missing_padding)
    
    try:
        image_data = base64.b64decode(base64_data)
        with open('figma_design_screenshot.png', 'wb') as f:
            f.write(image_data)
        print("✅ 修复填充后，图片已成功保存为 figma_design_screenshot.png")
        print(f"📏 图片大小: {len(image_data)} 字节")
    except Exception as e2:
        print(f"❌ 修复后仍然失败: {e2}")
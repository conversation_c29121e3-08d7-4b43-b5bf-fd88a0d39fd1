import UIKit

class WHSliderDemoViewController: UIViewController {

    // MARK: - UI Elements

    private let titleLabel: UILabel = {
        let label = UILabel()
        label.text = "WHSlider 演示"
        label.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        label.textAlignment = .center
        label.textColor = .label
        return label
    }()

    private let whSlider: WHSlider = {
        let slider = WHSlider()
        slider.value = 0.81 // 设置初始值为 81%，匹配设计稿中 73px/90px 的比例
        return slider
    }()

    private let valueLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.textAlignment = .center
        label.textColor = .secondaryLabel
        return label
    }()

    private let resetButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("重置为 80%", for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        button.backgroundColor = UIColor.systemBlue
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 8
        return button
    }()

    private let animateButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("动画到 50%", for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        button.backgroundColor = UIColor.systemGreen
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 8
        return button
    }()

    // MARK: - Lifecycle

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupActions()
        updateValueLabel()
    }

    // MARK: - Setup

    private func setupUI() {
        view.backgroundColor = .systemBackground

        // 添加子视图
        view.addSubview(titleLabel)
        view.addSubview(whSlider)
        view.addSubview(valueLabel)
        view.addSubview(resetButton)
        view.addSubview(animateButton)

        // 设置自动布局
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        whSlider.translatesAutoresizingMaskIntoConstraints = false
        valueLabel.translatesAutoresizingMaskIntoConstraints = false
        resetButton.translatesAutoresizingMaskIntoConstraints = false
        animateButton.translatesAutoresizingMaskIntoConstraints = false
    }

    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 标题标签
            titleLabel.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 40),
            titleLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),

            // WHSlider
            whSlider.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            whSlider.centerYAnchor.constraint(equalTo: view.centerYAnchor, constant: -50),
            whSlider.widthAnchor.constraint(equalToConstant: 200), // 比设计稿稍大一些便于演示
            whSlider.heightAnchor.constraint(equalToConstant: 40),

            // 值标签
            valueLabel.topAnchor.constraint(equalTo: whSlider.bottomAnchor, constant: 20),
            valueLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            valueLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),

            // 重置按钮
            resetButton.topAnchor.constraint(equalTo: valueLabel.bottomAnchor, constant: 40),
            resetButton.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 40),
            resetButton.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -40),
            resetButton.heightAnchor.constraint(equalToConstant: 44),

            // 动画按钮
            animateButton.topAnchor.constraint(equalTo: resetButton.bottomAnchor, constant: 16),
            animateButton.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 40),
            animateButton.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -40),
            animateButton.heightAnchor.constraint(equalToConstant: 44)
        ])
    }

    private func setupActions() {
        // 添加滑块值变化监听
        whSlider.addTarget(self, action: #selector(sliderValueChanged(_:)), for: .valueChanged)

        // 添加按钮点击事件
        resetButton.addTarget(self, action: #selector(resetButtonTapped), for: .touchUpInside)
        animateButton.addTarget(self, action: #selector(animateButtonTapped), for: .touchUpInside)
    }

    // MARK: - Actions

    @objc private func sliderValueChanged(_ slider: WHSlider) {
        updateValueLabel()
    }

    @objc private func resetButtonTapped() {
        whSlider.setValue(0.8, animated: true)
    }

    @objc private func animateButtonTapped() {
        whSlider.setValue(0.5, animated: true)
    }

    private func updateValueLabel() {
        let percentage = Int(whSlider.value * 100)
        valueLabel.text = "当前值: \(percentage)%"
    }
}
//
//  ViewController.swift
//  FigmaUITrae
//
//  Created by <PERSON><PERSON><PERSON> on 2025/8/7.
//

import UIKit
import SnapKit

class ViewController: UIViewController {
    
    private let titleLabel = UILabel()
    private let descriptionLabel = UILabel()
    private let actionButton = UIButton(type: .system)
    private let smartCutoffButton = UIButton(type: .system)
    private let newViewButton = UIButton(type: .system)
    private let customSliderButton = UIButton(type: .system)
    private let containerView = UIView()

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
    }
    
    private func setupUI() {
        view.backgroundColor = .systemBackground
        
        // 配置标题标签
        titleLabel.text = "SnapKit 演示"
        titleLabel.font = UIFont.boldSystemFont(ofSize: 24)
        titleLabel.textAlignment = .center
        titleLabel.textColor = .label
        
        // 配置描述标签
        descriptionLabel.text = "这是一个使用 SnapKit 进行自动布局的演示页面。SnapKit 让约束设置变得更加简洁和易读。"
        descriptionLabel.font = UIFont.systemFont(ofSize: 16)
        descriptionLabel.textAlignment = .center
        descriptionLabel.numberOfLines = 0
        descriptionLabel.textColor = .secondaryLabel
        
        // 配置测试按钮
        actionButton.setTitle("点击测试", for: .normal)
        actionButton.titleLabel?.font = UIFont.boldSystemFont(ofSize: 18)
        actionButton.backgroundColor = .systemBlue
        actionButton.setTitleColor(.white, for: .normal)
        actionButton.layer.cornerRadius = 8
        actionButton.addTarget(self, action: #selector(buttonTapped), for: .touchUpInside)
        
        // 配置智能截止按钮
        smartCutoffButton.setTitle("智能截止设置", for: .normal)
        smartCutoffButton.titleLabel?.font = UIFont.boldSystemFont(ofSize: 18)
        smartCutoffButton.backgroundColor = .systemPurple
        smartCutoffButton.setTitleColor(.white, for: .normal)
        smartCutoffButton.layer.cornerRadius = 8
        smartCutoffButton.addTarget(self, action: #selector(smartCutoffButtonTapped), for: .touchUpInside)
        
        // 配置新界面按钮
        newViewButton.setTitle("图像编辑器", for: .normal)
        newViewButton.titleLabel?.font = UIFont.boldSystemFont(ofSize: 18)
        newViewButton.backgroundColor = .systemGreen
        newViewButton.setTitleColor(.white, for: .normal)
        newViewButton.layer.cornerRadius = 8
        newViewButton.addTarget(self, action: #selector(newViewButtonTapped), for: .touchUpInside)
        
        // 配置自定义滑块按钮
        customSliderButton.setTitle("自定义 Slider", for: .normal)
        customSliderButton.titleLabel?.font = UIFont.boldSystemFont(ofSize: 18)
        customSliderButton.backgroundColor = .systemOrange
        customSliderButton.setTitleColor(.white, for: .normal)
        customSliderButton.layer.cornerRadius = 8
        customSliderButton.addTarget(self, action: #selector(customSliderButtonTapped), for: .touchUpInside)
        
        // 配置容器视图
        containerView.backgroundColor = .systemGray6
        containerView.layer.cornerRadius = 12
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOffset = CGSize(width: 0, height: 2)
        containerView.layer.shadowRadius = 4
        containerView.layer.shadowOpacity = 0.1
        
        // 添加子视图
        view.addSubview(containerView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(descriptionLabel)
        containerView.addSubview(actionButton)
        containerView.addSubview(smartCutoffButton)
        containerView.addSubview(newViewButton)
        containerView.addSubview(customSliderButton)
    }
    
    private func setupConstraints() {
        // 容器视图约束
        containerView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.left.right.equalToSuperview().inset(32)
            make.height.equalTo(480)
        }
        
        // 标题标签约束
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(32)
            make.left.right.equalToSuperview().inset(20)
        }
        
        // 描述标签约束
        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(20)
            make.left.right.equalToSuperview().inset(20)
        }
        
        // 测试按钮约束
        actionButton.snp.makeConstraints { make in
            make.bottom.equalTo(smartCutoffButton.snp.top).offset(-16)
            make.centerX.equalToSuperview()
            make.width.equalTo(150)
            make.height.equalTo(44)
        }
        
        // 智能截止按钮约束
        smartCutoffButton.snp.makeConstraints { make in
            make.bottom.equalTo(newViewButton.snp.top).offset(-16)
            make.centerX.equalToSuperview()
            make.width.equalTo(150)
            make.height.equalTo(44)
        }
        
        // 新界面按钮约束
        newViewButton.snp.makeConstraints { make in
            make.bottom.equalTo(customSliderButton.snp.top).offset(-16)
            make.centerX.equalToSuperview()
            make.width.equalTo(150)
            make.height.equalTo(44)
        }
        
        // 自定义滑块按钮约束
        customSliderButton.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-32)
            make.centerX.equalToSuperview()
            make.width.equalTo(150)
            make.height.equalTo(44)
        }
    }
    
    @objc private func buttonTapped() {
        // 简单的动画演示
        UIView.animate(withDuration: 0.3, animations: {
            self.actionButton.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }) { _ in
            UIView.animate(withDuration: 0.3) {
                self.actionButton.transform = CGAffineTransform.identity
            }
        }
        
        let alert = UIAlertController(title: "成功!", message: "SnapKit 自动布局工作正常", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    
    @objc private func smartCutoffButtonTapped() {
        // 导航到智能截止设置页面
        let smartCutoffVC = SmartCutofViewController()
        let navigationController = UINavigationController(rootViewController: smartCutoffVC)
        navigationController.modalPresentationStyle = .fullScreen
        present(navigationController, animated: true)
    }
    
    @objc private func newViewButtonTapped() {
        let newVC = NewViewController()
        let navigationController = UINavigationController(rootViewController: newVC)
        navigationController.modalPresentationStyle = UIModalPresentationStyle.fullScreen
        present(navigationController, animated: true)
    }
    
    @objc private func customSliderButtonTapped() {
        let customSliderVC = CustomSliderViewController()
        let navigationController = UINavigationController(rootViewController: customSliderVC)
        navigationController.modalPresentationStyle = .fullScreen
        present(navigationController, animated: true)
    }
}


//
//  SmartCutofViewController.swift
//  FigmaUITrae
//
//  Created by AI Assistant on 2025-01-16.
//  Redesigned with modern iOS design patterns
//

import UIKit
import SnapKit

class SmartCutofViewController: UIViewController {
    
    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    // Header Section
    private let headerContainerView = UIView()
    private let headerGradientLayer = CAGradientLayer()
    private let titleLabel = UILabel()
    private let subtitleLabel = UILabel()
    private let headerIconView = UIImageView()
    
    // Main Content Cards
    private let cardsStackView = UIStackView()
    
    // Smart Cutoff Main Card
    private let mainCard = UIView()
    private let mainCardIconView = UIImageView()
    private let mainCardTitleLabel = UILabel()
    private let mainCardSubtitleLabel = UILabel()
    private let mainToggleSwitch = UISwitch()
    
    // Settings Cards
    private let settingsCard = UIView()
    private let timeSettingView = UIView()
    private let batterySettingView = UIView()
    private let learningSettingView = UIView()
    
    // Time Setting Components
    private let timeIconView = UIImageView()
    private let timeTitleLabel = UILabel()
    private let timeSlider = UISlider()
    private let timeValueLabel = UILabel()
    private let timeDescLabel = UILabel()
    
    // Battery Setting Components
    private let batteryIconView = UIImageView()
    private let batteryTitleLabel = UILabel()
    private let batterySwitch = UISwitch()
    private let batteryDescLabel = UILabel()
    
    // Learning Setting Components
    private let learningIconView = UIImageView()
    private let learningTitleLabel = UILabel()
    private let learningSwitch = UISwitch()
    private let learningDescLabel = UILabel()
    
    // Action Button
    private let actionButton = UIButton(type: .system)
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupActions()
        setupAnimations()
        loadSettings()
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        setupGradients()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        view.backgroundColor = UIColor.systemGroupedBackground
        
        // Navigation
        setupNavigation()
        
        // Header
        setupHeader()
        
        // Cards
        setupCards()
        
        // Action Button
        setupActionButton()
        
        // Scroll View
        setupScrollView()
    }
    
    private func setupNavigation() {
        title = "智能截止"
        navigationController?.navigationBar.prefersLargeTitles = false
        
        let backButton = UIBarButtonItem(
            image: UIImage(systemName: "chevron.left")?.withConfiguration(UIImage.SymbolConfiguration(weight: .medium)),
            style: .plain,
            target: self,
            action: #selector(backButtonTapped)
        )
        backButton.tintColor = UIColor.label
        navigationItem.leftBarButtonItem = backButton
    }
    
    private func setupHeader() {
        headerContainerView.layer.cornerRadius = 20
        headerContainerView.layer.masksToBounds = true
        
        titleLabel.text = "智能截止"
        titleLabel.font = UIFont.systemFont(ofSize: 28, weight: .bold)
        titleLabel.textColor = UIColor.white
        
        subtitleLabel.text = "根据使用习惯智能调整应用限制"
        subtitleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        subtitleLabel.textColor = UIColor.white.withAlphaComponent(0.9)
        subtitleLabel.numberOfLines = 0
        
        headerIconView.image = UIImage(systemName: "brain.head.profile")?.withConfiguration(
            UIImage.SymbolConfiguration(pointSize: 40, weight: .medium)
        )
        headerIconView.tintColor = UIColor.white
        headerIconView.contentMode = .scaleAspectFit
    }
    
    private func setupCards() {
        cardsStackView.axis = .vertical
        cardsStackView.spacing = 16
        cardsStackView.distribution = .fill
        
        // Main Card
        setupMainCard()
        
        // Settings Card
        setupSettingsCard()
        
        cardsStackView.addArrangedSubview(mainCard)
        cardsStackView.addArrangedSubview(settingsCard)
    }
    
    private func setupMainCard() {
        mainCard.backgroundColor = UIColor.systemBackground
        mainCard.layer.cornerRadius = 16
        mainCard.layer.shadowColor = UIColor.black.cgColor
        mainCard.layer.shadowOffset = CGSize(width: 0, height: 2)
        mainCard.layer.shadowRadius = 8
        mainCard.layer.shadowOpacity = 0.1
        
        mainCardIconView.image = UIImage(systemName: "wand.and.stars")?.withConfiguration(
            UIImage.SymbolConfiguration(pointSize: 24, weight: .medium)
        )
        mainCardIconView.tintColor = UIColor.systemBlue
        mainCardIconView.contentMode = .scaleAspectFit
        
        mainCardTitleLabel.text = "启用智能截止"
        mainCardTitleLabel.font = UIFont.systemFont(ofSize: 20, weight: .semibold)
        mainCardTitleLabel.textColor = UIColor.label
        
        mainCardSubtitleLabel.text = "自动学习您的使用模式并调整限制"
        mainCardSubtitleLabel.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        mainCardSubtitleLabel.textColor = UIColor.secondaryLabel
        mainCardSubtitleLabel.numberOfLines = 0
        
        mainToggleSwitch.isOn = true
        mainToggleSwitch.onTintColor = UIColor.systemBlue
        mainToggleSwitch.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
        
        mainCard.addSubview(mainCardIconView)
        mainCard.addSubview(mainCardTitleLabel)
        mainCard.addSubview(mainCardSubtitleLabel)
        mainCard.addSubview(mainToggleSwitch)
    }
    
    private func setupSettingsCard() {
        settingsCard.backgroundColor = UIColor.systemBackground
        settingsCard.layer.cornerRadius = 16
        settingsCard.layer.shadowColor = UIColor.black.cgColor
        settingsCard.layer.shadowOffset = CGSize(width: 0, height: 2)
        settingsCard.layer.shadowRadius = 8
        settingsCard.layer.shadowOpacity = 0.1
        
        // Time Setting
        setupTimeSetting()
        
        // Battery Setting
        setupBatterySetting()
        
        // Learning Setting
        setupLearningSetting()
        
        // Add separators
        let separator1 = createSeparator()
        let separator2 = createSeparator()
        
        settingsCard.addSubview(timeSettingView)
        settingsCard.addSubview(separator1)
        settingsCard.addSubview(batterySettingView)
        settingsCard.addSubview(separator2)
        settingsCard.addSubview(learningSettingView)
    }
    
    private func setupTimeSetting() {
        timeIconView.image = UIImage(systemName: "clock.fill")?.withConfiguration(
            UIImage.SymbolConfiguration(pointSize: 20, weight: .medium)
        )
        timeIconView.tintColor = UIColor.systemOrange
        
        timeTitleLabel.text = "时间敏感度"
        timeTitleLabel.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        timeTitleLabel.textColor = UIColor.label
        
        timeDescLabel.text = "调整智能截止的敏感程度"
        timeDescLabel.font = UIFont.systemFont(ofSize: 13, weight: .regular)
        timeDescLabel.textColor = UIColor.secondaryLabel
        
        timeSlider.minimumValue = 1
        timeSlider.maximumValue = 10
        timeSlider.value = 5
        timeSlider.tintColor = UIColor.systemOrange
        
        timeValueLabel.text = "5"
        timeValueLabel.font = UIFont.systemFont(ofSize: 16, weight: .bold)
        timeValueLabel.textColor = UIColor.systemOrange
        timeValueLabel.textAlignment = .center
        
        timeSettingView.addSubview(timeIconView)
        timeSettingView.addSubview(timeTitleLabel)
        timeSettingView.addSubview(timeDescLabel)
        timeSettingView.addSubview(timeSlider)
        timeSettingView.addSubview(timeValueLabel)
    }
    
    private func setupBatterySetting() {
        batteryIconView.image = UIImage(systemName: "battery.100")?.withConfiguration(
            UIImage.SymbolConfiguration(pointSize: 20, weight: .medium)
        )
        batteryIconView.tintColor = UIColor.systemGreen
        
        batteryTitleLabel.text = "电池优化"
        batteryTitleLabel.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        batteryTitleLabel.textColor = UIColor.label
        
        batteryDescLabel.text = "低电量时自动减少使用时间"
        batteryDescLabel.font = UIFont.systemFont(ofSize: 13, weight: .regular)
        batteryDescLabel.textColor = UIColor.secondaryLabel
        
        batterySwitch.isOn = false
        batterySwitch.onTintColor = UIColor.systemGreen
        batterySwitch.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
        
        batterySettingView.addSubview(batteryIconView)
        batterySettingView.addSubview(batteryTitleLabel)
        batterySettingView.addSubview(batteryDescLabel)
        batterySettingView.addSubview(batterySwitch)
    }
    
    private func setupLearningSetting() {
        learningIconView.image = UIImage(systemName: "brain")?.withConfiguration(
            UIImage.SymbolConfiguration(pointSize: 20, weight: .medium)
        )
        learningIconView.tintColor = UIColor.systemPurple
        
        learningTitleLabel.text = "使用模式学习"
        learningTitleLabel.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        learningTitleLabel.textColor = UIColor.label
        
        learningDescLabel.text = "学习您的习惯提供个性化建议"
        learningDescLabel.font = UIFont.systemFont(ofSize: 13, weight: .regular)
        learningDescLabel.textColor = UIColor.secondaryLabel
        
        learningSwitch.isOn = true
        learningSwitch.onTintColor = UIColor.systemPurple
        learningSwitch.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
        
        learningSettingView.addSubview(learningIconView)
        learningSettingView.addSubview(learningTitleLabel)
        learningSettingView.addSubview(learningDescLabel)
        learningSettingView.addSubview(learningSwitch)
    }
    
    private func setupActionButton() {
        actionButton.setTitle("保存设置", for: .normal)
        actionButton.titleLabel?.font = UIFont.systemFont(ofSize: 17, weight: .semibold)
        actionButton.backgroundColor = UIColor.systemBlue
        actionButton.setTitleColor(UIColor.white, for: .normal)
        actionButton.layer.cornerRadius = 14
        actionButton.layer.shadowColor = UIColor.systemBlue.cgColor
        actionButton.layer.shadowOffset = CGSize(width: 0, height: 4)
        actionButton.layer.shadowRadius = 12
        actionButton.layer.shadowOpacity = 0.3
    }
    
    private func setupScrollView() {
        scrollView.showsVerticalScrollIndicator = false
        scrollView.contentInsetAdjustmentBehavior = .automatic
        
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        contentView.addSubview(headerContainerView)
        headerContainerView.addSubview(titleLabel)
        headerContainerView.addSubview(subtitleLabel)
        headerContainerView.addSubview(headerIconView)
        
        contentView.addSubview(cardsStackView)
        contentView.addSubview(actionButton)
    }
    
    private func createSeparator() -> UIView {
        let separator = UIView()
        separator.backgroundColor = UIColor.separator
        return separator
    }
    
    private func setupGradients() {
        headerGradientLayer.colors = [
            UIColor.systemBlue.cgColor,
            UIColor.systemPurple.cgColor
        ]
        headerGradientLayer.startPoint = CGPoint(x: 0, y: 0)
        headerGradientLayer.endPoint = CGPoint(x: 1, y: 1)
        headerGradientLayer.frame = headerContainerView.bounds
        headerContainerView.layer.insertSublayer(headerGradientLayer, at: 0)
    }
    
    // MARK: - Constraints
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        headerContainerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(120)
        }
        
        headerIconView.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-24)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(50)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(24)
            make.leading.equalToSuperview().offset(24)
            make.trailing.equalTo(headerIconView.snp.leading).offset(-16)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.equalToSuperview().offset(24)
            make.trailing.equalTo(headerIconView.snp.leading).offset(-16)
        }
        
        cardsStackView.snp.makeConstraints { make in
            make.top.equalTo(headerContainerView.snp.bottom).offset(24)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        // Main Card Constraints
        mainCardIconView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.equalToSuperview().offset(20)
            make.width.height.equalTo(28)
        }
        
        mainCardTitleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.equalTo(mainCardIconView.snp.trailing).offset(12)
            make.trailing.equalTo(mainToggleSwitch.snp.leading).offset(-12)
        }
        
        mainCardSubtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(mainCardTitleLabel.snp.bottom).offset(4)
            make.leading.equalTo(mainCardIconView.snp.trailing).offset(12)
            make.trailing.equalTo(mainToggleSwitch.snp.leading).offset(-12)
            make.bottom.equalToSuperview().offset(-20)
        }
        
        mainToggleSwitch.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview().offset(-20)
        }
        
        // Settings Card Constraints
        timeSettingView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(80)
        }
        
        timeIconView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.leading.equalToSuperview().offset(20)
            make.width.height.equalTo(24)
        }
        
        timeTitleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.leading.equalTo(timeIconView.snp.trailing).offset(12)
        }
        
        timeDescLabel.snp.makeConstraints { make in
            make.top.equalTo(timeTitleLabel.snp.bottom).offset(2)
            make.leading.equalTo(timeIconView.snp.trailing).offset(12)
        }
        
        timeValueLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.trailing.equalToSuperview().offset(-20)
            make.width.equalTo(30)
        }
        
        timeSlider.snp.makeConstraints { make in
            make.top.equalTo(timeDescLabel.snp.bottom).offset(8)
            make.leading.equalTo(timeIconView.snp.trailing).offset(12)
            make.trailing.equalTo(timeValueLabel.snp.leading).offset(-12)
        }
        
        // Battery Setting Constraints
        let separator1 = settingsCard.subviews.first { $0.backgroundColor == UIColor.separator }
        separator1?.snp.makeConstraints { make in
            make.top.equalTo(timeSettingView.snp.bottom)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(0.5)
        }
        
        batterySettingView.snp.makeConstraints { make in
            make.top.equalTo(timeSettingView.snp.bottom).offset(0.5)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(60)
        }
        
        batteryIconView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalToSuperview().offset(20)
            make.width.height.equalTo(24)
        }
        
        batteryTitleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.leading.equalTo(batteryIconView.snp.trailing).offset(12)
        }
        
        batteryDescLabel.snp.makeConstraints { make in
            make.top.equalTo(batteryTitleLabel.snp.bottom).offset(2)
            make.leading.equalTo(batteryIconView.snp.trailing).offset(12)
        }
        
        batterySwitch.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview().offset(-20)
        }
        
        // Learning Setting Constraints
        let separator2 = settingsCard.subviews.last { $0.backgroundColor == UIColor.separator }
        separator2?.snp.makeConstraints { make in
            make.top.equalTo(batterySettingView.snp.bottom)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(0.5)
        }
        
        learningSettingView.snp.makeConstraints { make in
            make.top.equalTo(batterySettingView.snp.bottom).offset(0.5)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(60)
            make.bottom.equalToSuperview()
        }
        
        learningIconView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalToSuperview().offset(20)
            make.width.height.equalTo(24)
        }
        
        learningTitleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.leading.equalTo(learningIconView.snp.trailing).offset(12)
        }
        
        learningDescLabel.snp.makeConstraints { make in
            make.top.equalTo(learningTitleLabel.snp.bottom).offset(2)
            make.leading.equalTo(learningIconView.snp.trailing).offset(12)
        }
        
        learningSwitch.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview().offset(-20)
        }
        
        // Action Button
        actionButton.snp.makeConstraints { make in
            make.top.equalTo(cardsStackView.snp.bottom).offset(32)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(56)
            make.bottom.equalToSuperview().offset(-32)
        }
    }
    
    // MARK: - Actions
    private func setupActions() {
        timeSlider.addTarget(self, action: #selector(timeSliderChanged), for: .valueChanged)
        actionButton.addTarget(self, action: #selector(saveButtonTapped), for: .touchUpInside)
        mainToggleSwitch.addTarget(self, action: #selector(mainToggleChanged), for: .valueChanged)
    }
    
    private func setupAnimations() {
        // Add subtle animations for better UX
        actionButton.addTarget(self, action: #selector(buttonTouchDown), for: .touchDown)
        actionButton.addTarget(self, action: #selector(buttonTouchUp), for: [.touchUpInside, .touchUpOutside, .touchCancel])
    }
    
    @objc private func backButtonTapped() {
        navigationController?.popViewController(animated: true)
        self.dismiss(animated: true)
    }
    
    @objc private func timeSliderChanged() {
        let value = Int(timeSlider.value)
        timeValueLabel.text = "\(value)"
        
        // Add haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
    
    @objc private func mainToggleChanged() {
        // Add haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        // Animate settings card based on toggle state
        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseInOut) {
            self.settingsCard.alpha = self.mainToggleSwitch.isOn ? 1.0 : 0.5
        }
    }
    
    @objc private func saveButtonTapped() {
        // Add haptic feedback
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.success)
        
        // Save settings logic
        saveSettings()
        
        let alert = UIAlertController(title: "设置已保存", message: "您的智能截止设置已成功保存", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    
    @objc private func buttonTouchDown() {
        UIView.animate(withDuration: 0.1) {
            self.actionButton.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }
    }
    
    @objc private func buttonTouchUp() {
        UIView.animate(withDuration: 0.1) {
            self.actionButton.transform = CGAffineTransform.identity
        }
    }
    
    // MARK: - Settings Management
    private func loadSettings() {
        // Load saved settings from UserDefaults
        let defaults = UserDefaults.standard
        mainToggleSwitch.isOn = defaults.bool(forKey: "smartCutoffEnabled")
        timeSlider.value = defaults.float(forKey: "timeSensitivity")
        batterySwitch.isOn = defaults.bool(forKey: "batteryOptimization")
        learningSwitch.isOn = defaults.bool(forKey: "usageLearning")
        
        timeValueLabel.text = "\(Int(timeSlider.value))"
        
        // Update settings card alpha based on main toggle
        settingsCard.alpha = mainToggleSwitch.isOn ? 1.0 : 0.5
    }
    
    private func saveSettings() {
        // Save current settings to UserDefaults
        let defaults = UserDefaults.standard
        defaults.set(mainToggleSwitch.isOn, forKey: "smartCutoffEnabled")
        defaults.set(timeSlider.value, forKey: "timeSensitivity")
        defaults.set(batterySwitch.isOn, forKey: "batteryOptimization")
        defaults.set(learningSwitch.isOn, forKey: "usageLearning")
    }
}

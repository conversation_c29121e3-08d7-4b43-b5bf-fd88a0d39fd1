//
//  NewViewController.swift
//  FigmaUITrae
//
//  Created by Assistant on 2025-01-07.
//  Updated to match Figma design on 2025-01-07.
//

import UIKit
import SnapKit

class NewViewController: UIViewController {
    
    // MARK: - UI Components
    private let mainContainerView = UIView()
    
    // Status Bar (simulated)
    private let statusBarView = UIView()
    private let timeLabel = UILabel()
    private let batteryView = UIView()
    private let signalView = UIView()
    
    // Navigation Bar Components
    private let navigationBarView = UIView()
    private let backButton = UIButton(type: .custom)
    private let titleLabel = UILabel()
    private let exportButton = UIButton(type: .custom)
    
    // Main Content Area
    private let contentAreaView = UIView()
    private let mainImageView = UIImageView()
    
    // Sidebar Components
    private let sidebarView = UIView()
    private let undoButton = UIButton(type: .custom)
    private let redoButton = UIButton(type: .custom)
    private let layerButton = UIButton(type: .custom)
    private let progressSlider = UISlider()
    
    // Detail Panel (bottom)
    private let detailPanel = UIView()
    private let segmentedControl = UISegmentedControl(items: ["HD", "Ultra HD"])
    private let qualitySlider = UISlider()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupActions()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.setNavigationBarHidden(true, animated: animated)
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        view.backgroundColor = UIColor.systemBackground
        
        // 添加工具栏到视图
        let toolBarView = ToolBarView()
        view.addSubview(toolBarView)
        
        // 设置工具栏约束
        toolBarView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-20)
            make.leading.greaterThanOrEqualToSuperview().offset(20)
            make.trailing.lessThanOrEqualToSuperview().offset(-20)
        }
    }
    
    
    // MARK: - Constraints
    private func setupConstraints() {
    }
    
    // MARK: - Actions
    private func setupActions() {
    }
    

}

// MARK: - Dark Mode Support
extension NewViewController {
    override func traitCollectionDidChange(_ previousTraitCollection: UITraitCollection?) {
        super.traitCollectionDidChange(previousTraitCollection)
        
        if traitCollection.hasDifferentColorAppearance(comparedTo: previousTraitCollection) {
            // Update colors for dark mode
            updateColorsForCurrentTraitCollection()
        }
    }
    
    private func updateColorsForCurrentTraitCollection() {
        // Update shadow colors for dark mode
        navigationBarView.layer.shadowColor = UIColor.label.cgColor
        sidebarView.layer.shadowColor = UIColor.label.cgColor
        detailPanel.layer.shadowColor = UIColor.label.cgColor
    }
    
    // MARK: - Card Action Methods
    @objc private func historyCardTapped() {
        print("操作历史卡片被点击")
        
        // 添加点击动画效果
        animateCardTap(sender: nil)
        
        // TODO: 实现操作历史功能
        // 可以显示撤销/重做历史列表
        showHistoryOptions()
    }
    
    @objc private func layerCardTapped() {
        print("图层管理卡片被点击")
        
        // 添加点击动画效果
        animateCardTap(sender: nil)
        
        // TODO: 实现图层管理功能
        // 可以显示图层列表、可见性控制等
        showLayerManagement()
    }
    
    @objc private func exportCardTapped() {
        print("导出分享卡片被点击")
        
        // 添加点击动画效果
        animateCardTap(sender: nil)
        
        // TODO: 实现导出分享功能
        // 可以显示导出选项、分享功能等
        showExportOptions()
    }
    
    // MARK: - Animation Methods
    private func animateCardTap(sender: UIView?) {
        guard let cardView = sender else { return }
        
        // 缩放动画
        UIView.animate(withDuration: 0.1, animations: {
            cardView.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                cardView.transform = CGAffineTransform.identity
            }
        }
    }
    
    // MARK: - Feature Implementation Stubs
    private func showHistoryOptions() {
        // 显示操作历史选项
        let alert = UIAlertController(title: "操作历史", message: "选择操作", preferredStyle: .actionSheet)
        
        alert.addAction(UIAlertAction(title: "撤销", style: .default) { _ in
            print("执行撤销操作")
        })
        
        alert.addAction(UIAlertAction(title: "重做", style: .default) { _ in
            print("执行重做操作")
        })
        
        alert.addAction(UIAlertAction(title: "历史记录", style: .default) { _ in
            print("显示历史记录")
        })
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        present(alert, animated: true)
    }
    
    private func showLayerManagement() {
        // 显示图层管理选项
        let alert = UIAlertController(title: "图层管理", message: "选择操作", preferredStyle: .actionSheet)
        
        alert.addAction(UIAlertAction(title: "显示图层", style: .default) { _ in
            print("显示图层列表")
        })
        
        alert.addAction(UIAlertAction(title: "隐藏图层", style: .default) { _ in
            print("隐藏图层")
        })
        
        alert.addAction(UIAlertAction(title: "图层设置", style: .default) { _ in
            print("图层设置")
        })
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        present(alert, animated: true)
    }
    
    private func showExportOptions() {
        // 显示导出选项
        let alert = UIAlertController(title: "导出分享", message: "选择操作", preferredStyle: .actionSheet)
        
        alert.addAction(UIAlertAction(title: "导出图片", style: .default) { _ in
            print("导出为图片")
        })
        
        alert.addAction(UIAlertAction(title: "导出PDF", style: .default) { _ in
            print("导出为PDF")
        })
        
        alert.addAction(UIAlertAction(title: "分享", style: .default) { _ in
            print("分享功能")
        })
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        present(alert, animated: true)
    }
}

// MARK: - ToolBarView Redesign Implementation
extension NewViewController {
    
    @objc func ToolBarView() -> UIView {
        let containerView = createToolBarContainer()
        let cardsStackView = createCardsStackView()
        let progressContainer = createProgressContainer()
        
        // 创建功能卡片
        let historyCard = createHistoryCard()
        let layerCard = createLayerCard()
        let exportCard = createExportCard()
        
        // 组装卡片
        [historyCard, layerCard, exportCard].forEach {
            cardsStackView.addArrangedSubview($0)
        }
        
        // 组装容器
        containerView.addSubview(cardsStackView)
        containerView.addSubview(progressContainer)
        
        // 设置约束
        setupToolBarConstraints(
            container: containerView,
            cardsStack: cardsStackView,
            progressContainer: progressContainer
        )
        
        // 设置无障碍支持
        setupProgressAccessibility(progressContainer)
        
        // 添加动画效果
        addCardHoverEffect(historyCard)
        addCardHoverEffect(layerCard)
        addCardHoverEffect(exportCard)
        
        return containerView
    }
    
    // MARK: - ToolBar Container Methods
    private func createToolBarContainer() -> UIView {
        let container = UIView()
        container.backgroundColor = UIColor(red: 0.17, green: 0.17, blue: 0.18, alpha: 1.0) // #2C2C2E
        container.layer.cornerRadius = 16
        container.layer.masksToBounds = true
        
        // 添加轻微的边框效果
        container.layer.borderWidth = 0.5
        container.layer.borderColor = UIColor.white.withAlphaComponent(0.1).cgColor
        
        return container
    }
    
    private func createCardsStackView() -> UIStackView {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.alignment = .center
        stackView.spacing = 20
        return stackView
    }
    
    private func createProgressContainer() -> UIView {
        let container = UIView()
        let progressBar = UIProgressView(progressViewStyle: .default)
        
        // 配置进度条样式
        progressBar.progressTintColor = UIColor.white
        progressBar.trackTintColor = UIColor.white.withAlphaComponent(0.3)
        progressBar.layer.cornerRadius = 2
        progressBar.clipsToBounds = true
        progressBar.progress = 0.6 // 默认进度
        
        container.addSubview(progressBar)
        
        progressBar.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(4)
        }
        
        // 存储进度条引用
        container.tag = 999 // 用于后续查找
        
        return container
    }
    
    // MARK: - Card Creation Methods
    private func createHistoryCard() -> UIView {
        return createFunctionCard(
            iconName: "undo_icon",
            title: "操作历史",
            action: #selector(historyCardTapped)
        )
    }
    
    private func createLayerCard() -> UIView {
        return createFunctionCard(
            systemIconName: "square.3.layers.3d",
            title: "图层管理",
            action: #selector(layerCardTapped)
        )
    }
    
    private func createExportCard() -> UIView {
        return createFunctionCard(
            iconName: "download_icon",
            title: "导出分享",
            action: #selector(exportCardTapped)
        )
    }
    
    private func createFunctionCard(
        iconName: String? = nil,
        systemIconName: String? = nil,
        title: String,
        action: Selector
    ) -> UIView {
        let cardView = UIView()
        let iconImageView = UIImageView()
        let titleLabel = UILabel()
        
        // 配置卡片样式
        cardView.backgroundColor = UIColor(red: 0.28, green: 0.28, blue: 0.29, alpha: 1.0) // #48484A
        cardView.layer.cornerRadius = 12
        cardView.layer.masksToBounds = true
        
        // 添加轻微的内阴影效果
        cardView.layer.borderWidth = 0.5
        cardView.layer.borderColor = UIColor.white.withAlphaComponent(0.05).cgColor
        
        // 配置图标
        if let iconName = iconName {
            iconImageView.image = UIImage(named: iconName)?.withRenderingMode(.alwaysTemplate)
        } else if let systemIconName = systemIconName {
            iconImageView.image = UIImage(systemName: systemIconName)
        }
        
        iconImageView.tintColor = UIColor.white
        iconImageView.contentMode = .scaleAspectFit
        
        // 配置标签
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        titleLabel.textColor = UIColor.white
        titleLabel.textAlignment = .center
        titleLabel.numberOfLines = 1
        
        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: action)
        cardView.addGestureRecognizer(tapGesture)
        cardView.isUserInteractionEnabled = true
        
        // 配置无障碍支持
        setupCardAccessibility(cardView, title: title)
        
        // 布局子视图
        cardView.addSubview(iconImageView)
        cardView.addSubview(titleLabel)
        
        setupCardConstraints(cardView, iconImageView, titleLabel)
        
        return cardView
    }
    
    // MARK: - Constraints Setup
    private func setupToolBarConstraints(
        container: UIView,
        cardsStack: UIStackView,
        progressContainer: UIView
    ) {
        // 容器约束
        container.snp.makeConstraints { make in
            make.height.equalTo(120)
        }
        
        // 卡片容器约束
        cardsStack.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(80)
        }
        
        // 进度条容器约束
        progressContainer.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-16)
            make.leading.trailing.equalToSuperview().inset(24)
            make.height.equalTo(4)
        }
    }
    
    private func setupCardConstraints(
        _ card: UIView,
        _ icon: UIImageView,
        _ label: UILabel
    ) {
        // 卡片尺寸
        card.snp.makeConstraints { make in
            make.width.equalTo(80)
            make.height.equalTo(80)
        }
        
        // 图标约束
        icon.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(16)
            make.size.equalTo(24)
        }
        
        // 标签约束
         label.snp.makeConstraints { make in
             make.centerX.equalToSuperview()
             make.bottom.equalToSuperview().offset(-12)
             make.leading.trailing.equalToSuperview().inset(6)
         }
     }
     
     // MARK: - Accessibility Support
     private func setupCardAccessibility(_ card: UIView, title: String) {
         card.isAccessibilityElement = true
         card.accessibilityLabel = title
         card.accessibilityTraits = .button
         
         switch title {
         case "操作历史":
             card.accessibilityHint = "双击查看撤销和重做选项"
         case "图层管理":
             card.accessibilityHint = "双击管理图层显示和设置"
         case "导出分享":
             card.accessibilityHint = "双击查看导出和分享选项"
         default:
             card.accessibilityHint = "双击执行操作"
         }
     }
     
     private func setupProgressAccessibility(_ progressContainer: UIView) {
         if let progressBar = progressContainer.subviews.first as? UIProgressView {
             progressBar.isAccessibilityElement = true
             progressBar.accessibilityLabel = "处理进度"
             progressBar.accessibilityValue = "\(Int(progressBar.progress * 100))%"
             progressBar.accessibilityTraits = .updatesFrequently
         }
     }
     
     // MARK: - Enhanced Animation Effects
     private func addCardHoverEffect(_ card: UIView) {
         // 添加轻微的阴影效果
         card.layer.shadowColor = UIColor.white.cgColor
         card.layer.shadowOpacity = 0.0
         card.layer.shadowOffset = CGSize(width: 0, height: 2)
         card.layer.shadowRadius = 4
         
         // 添加长按手势以提供额外反馈
         let longPressGesture = UILongPressGestureRecognizer(target: self, action: #selector(cardLongPressed(_:)))
         longPressGesture.minimumPressDuration = 0.5
         card.addGestureRecognizer(longPressGesture)
     }
     
     @objc private func cardLongPressed(_ gesture: UILongPressGestureRecognizer) {
         guard let cardView = gesture.view else { return }
         
         if gesture.state == .began {
             // 长按开始时的动画
             UIView.animate(withDuration: 0.2) {
                 cardView.layer.shadowOpacity = 0.3
                 cardView.transform = CGAffineTransform(scaleX: 1.05, y: 1.05)
             }
         } else if gesture.state == .ended || gesture.state == .cancelled {
             // 长按结束时的动画
             UIView.animate(withDuration: 0.2) {
                 cardView.layer.shadowOpacity = 0.0
                 cardView.transform = CGAffineTransform.identity
             }
         }
     }
}

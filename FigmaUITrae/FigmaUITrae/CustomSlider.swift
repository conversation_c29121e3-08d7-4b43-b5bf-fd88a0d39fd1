//
//  CustomSlider.swift
//  FigmaUITrae
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/27.
//

import UIKit
import SnapKit

class CustomSlider: UIView {
    
    // MARK: - Properties
    private let backgroundView = UIView()
    private let activeTrackView = UIView()
    private let thumbView = UIView()
    private let activeLoadImageView = UIImageView()
    
    private var panGesture: UIPanGestureRecognizer!
    private var initialThumbCenter: CGPoint = .zero
    
    // Slider properties
    var minimumValue: Float = 0.0
    var maximumValue: Float = 1.0
    private var _value: Float = 0.0
    
    var value: Float {
        get { return _value }
        set {
            _value = max(minimumValue, min(maximumValue, newValue))
            updateSliderAppearance()
        }
    }
    
    // Callback for value changes
    var valueChangedCallback: ((Float) -> Void)?
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupGestures()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        setupGestures()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        // 设置背景视图 - 深色背景 #24262b
        backgroundView.backgroundColor = UIColor(red: 0x24/255.0, green: 0x26/255.0, blue: 0x2b/255.0, alpha: 1.0)
        backgroundView.layer.cornerRadius = 12
        backgroundView.clipsToBounds = true
        
        // 设置活跃轨道视图 - 蓝色 #3549ff
        activeTrackView.backgroundColor = UIColor(red: 0x35/255.0, green: 0x49/255.0, blue: 0xff/255.0, alpha: 1.0)
        activeTrackView.layer.cornerRadius = 12
        activeTrackView.clipsToBounds = true
        
        // 设置滑块图标
        setupActiveLoadImage()
        
        // 添加子视图
        addSubview(backgroundView)
        backgroundView.addSubview(activeTrackView)
        backgroundView.addSubview(activeLoadImageView)
        
        setupConstraints()
    }
    
    private func setupActiveLoadImage() {
        // 创建 ActiveLoad 图标的 SVG 路径
        let image = createActiveLoadImage()
        activeLoadImageView.image = image
        activeLoadImageView.contentMode = .scaleAspectFit
        activeLoadImageView.tintColor = .white
    }
    
    private func createActiveLoadImage() -> UIImage? {
        // 创建一个简单的加载图标 (三个点)
        let size = CGSize(width: 90, height: 20)
        let renderer = UIGraphicsImageRenderer(size: size)
        
        return renderer.image { context in
            let cgContext = context.cgContext
            cgContext.setFillColor(UIColor.white.cgColor)
            
            // 绘制三个圆点
            let dotRadius: CGFloat = 3
            let spacing: CGFloat = 12
            let startX = (size.width - (3 * dotRadius * 2 + 2 * spacing)) / 2
            let centerY = size.height / 2
            
            for i in 0..<3 {
                let x = startX + CGFloat(i) * (dotRadius * 2 + spacing) + dotRadius
                let rect = CGRect(x: x - dotRadius, y: centerY - dotRadius, width: dotRadius * 2, height: dotRadius * 2)
                cgContext.fillEllipse(in: rect)
            }
        }
    }
    
    private func setupConstraints() {
        // 背景视图约束
        backgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 活跃轨道视图约束
        activeTrackView.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalTo(73) // 初始宽度
        }
        
        // 活跃加载图标约束
        activeLoadImageView.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(90)
            make.height.equalTo(20)
        }
    }
    
    // MARK: - Gestures
    private func setupGestures() {
        panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePanGesture(_:)))
        addGestureRecognizer(panGesture)
    }
    
    @objc private func handlePanGesture(_ gesture: UIPanGestureRecognizer) {
        let location = gesture.location(in: self)
        
        switch gesture.state {
        case .began:
            initialThumbCenter = location
        case .changed:
            updateValueFromLocation(location)
        case .ended, .cancelled:
            break
        default:
            break
        }
    }
    
    private func updateValueFromLocation(_ location: CGPoint) {
        let trackWidth = bounds.width
        let percentage = max(0, min(1, location.x / trackWidth))
        let newValue = minimumValue + Float(percentage) * (maximumValue - minimumValue)
        
        if newValue != _value {
            _value = newValue
            updateSliderAppearance()
            valueChangedCallback?(_value)
        }
    }
    
    private func updateSliderAppearance() {
        let percentage = CGFloat((_value - minimumValue) / (maximumValue - minimumValue))
        let trackWidth = bounds.width
        let activeWidth = max(73, trackWidth * percentage) // 最小宽度 73
        
        activeTrackView.snp.updateConstraints { make in
            make.width.equalTo(activeWidth)
        }
        
        UIView.animate(withDuration: 0.1) {
            self.layoutIfNeeded()
        }
    }
    
    // MARK: - Layout
    override func layoutSubviews() {
        super.layoutSubviews()
        updateSliderAppearance()
    }
}

// MARK: - Public Methods
extension CustomSlider {
    func setValue(_ value: Float, animated: Bool) {
        self._value = max(minimumValue, min(maximumValue, value))
        
        if animated {
            UIView.animate(withDuration: 0.3) {
                self.updateSliderAppearance()
            }
        } else {
            updateSliderAppearance()
        }
    }
}
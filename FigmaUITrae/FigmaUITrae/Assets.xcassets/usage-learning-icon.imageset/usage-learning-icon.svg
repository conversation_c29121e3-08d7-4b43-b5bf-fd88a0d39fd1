<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Brain/Learning center -->
  <path d="M12 3C8.5 3 6 5.5 6 9C6 10.2 6.3 11.3 6.8 12.2C6.3 13.1 6 14.2 6 15.4C6 18.5 8.5 21 12 21C15.5 21 18 18.5 18 15.4C18 14.2 17.7 13.1 17.2 12.2C17.7 11.3 18 10.2 18 9C18 5.5 15.5 3 12 3Z" stroke="#AF52DE" stroke-width="1.5" fill="none"/>
  
  <!-- Neural network connections -->
  <circle cx="9" cy="8" r="1" fill="#AF52DE" opacity="0.8"/>
  <circle cx="15" cy="8" r="1" fill="#AF52DE" opacity="0.8"/>
  <circle cx="12" cy="11" r="1" fill="#AF52DE" opacity="0.8"/>
  <circle cx="9" cy="14" r="1" fill="#AF52DE" opacity="0.8"/>
  <circle cx="15" cy="14" r="1" fill="#AF52DE" opacity="0.8"/>
  
  <!-- Connection lines -->
  <path d="M9 8L12 11L15 8" stroke="#AF52DE" stroke-width="1" opacity="0.6"/>
  <path d="M9 14L12 11L15 14" stroke="#AF52DE" stroke-width="1" opacity="0.6"/>
  <path d="M9 8L9 14" stroke="#AF52DE" stroke-width="1" opacity="0.4"/>
  <path d="M15 8L15 14" stroke="#AF52DE" stroke-width="1" opacity="0.4"/>
  
  <!-- Learning progress indicator -->
  <g transform="translate(16, 16)">
    <circle cx="2" cy="2" r="3" stroke="#AF52DE" stroke-width="1" fill="white" opacity="0.9"/>
    <path d="M1 2L2 3L3.5 1.5" stroke="#AF52DE" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"/>
  </g>
  
  <!-- Data flow indicators -->
  <path d="M4 6L6 8" stroke="#AF52DE" stroke-width="1" stroke-linecap="round" opacity="0.5"/>
  <path d="M20 6L18 8" stroke="#AF52DE" stroke-width="1" stroke-linecap="round" opacity="0.5"/>
  <path d="M4 18L6 16" stroke="#AF52DE" stroke-width="1" stroke-linecap="round" opacity="0.5"/>
  <path d="M20 18L18 16" stroke="#AF52DE" stroke-width="1" stroke-linecap="round" opacity="0.5"/>
</svg>
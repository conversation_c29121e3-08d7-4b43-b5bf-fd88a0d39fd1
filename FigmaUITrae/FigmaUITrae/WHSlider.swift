import UIKit

@IBDesignable
class WHSlider: UIControl {

    // MARK: - Properties

    /// 滑块的当前值 (0.0 - 1.0)
    @IBInspectable var value: Float = 0.0 {
        didSet {
            value = max(0.0, min(1.0, value))
            updateSliderAppearance()
        }
    }

    /// 滑块背景色
    @IBInspectable var trackColor: UIColor = UIColor(red: 0x24/255.0, green: 0x26/255.0, blue: 0x2b/255.0, alpha: 1.0) {
        didSet {
            backgroundView.backgroundColor = trackColor
        }
    }

    /// 滑块激活区域颜色
    @IBInspectable var activeColor: UIColor = UIColor(red: 0x35/255.0, green: 0x49/255.0, blue: 0xff/255.0, alpha: 1.0) {
        didSet {
            activeView.backgroundColor = activeColor
        }
    }

    /// 滑块高度
    @IBInspectable var sliderHeight: CGFloat = 20.0 {
        didSet {
            invalidateIntrinsicContentSize()
            setNeedsLayout()
        }
    }

    /// 操作钮大小
    @IBInspectable var thumbSize: CGFloat = 16.0 {
        didSet {
            setNeedsLayout()
        }
    }

    // MARK: - Private Properties

    private let backgroundView = UIView()
    private let activeView = UIView()
    private let activeLoadImageView = UIImageView()
    private let thumbView = UIView() // 白色操作钮

    override var isTracking = false
    private var trackingStartValue: Float = 0.0

    // MARK: - Initialization

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupViews()
    }

    // MARK: - Setup

    private func setupViews() {
        // 设置背景视图 - 使用 rounded-xl (12px 圆角)
        backgroundView.backgroundColor = trackColor
        backgroundView.layer.cornerRadius = 12
        backgroundView.isUserInteractionEnabled = false
        addSubview(backgroundView)

        // 设置激活区域视图 - 使用 rounded-xl (12px 圆角)
        activeView.backgroundColor = activeColor
        activeView.layer.cornerRadius = 12
        activeView.isUserInteractionEnabled = false
        addSubview(activeView)

        // 设置 ActiveLoad 图片
        setupActiveLoadImage()
        addSubview(activeLoadImageView)

        // 设置白色操作钮
        setupThumb()
        addSubview(thumbView)

        // 初始化外观
        updateSliderAppearance()
    }

    private func setupActiveLoadImage() {
        // 设置 ActiveLoad 图片 (基于设计稿中的图标)
        activeLoadImageView.contentMode = .scaleAspectFit
        activeLoadImageView.isUserInteractionEnabled = false

        // 如果有具体的图标资源，可以设置：
        // activeLoadImageView.image = UIImage(named: "active_load_icon")

        // 临时使用占位符，实际应该使用设计稿中的 ActiveLoad 图片
        activeLoadImageView.backgroundColor = UIColor.white.withAlphaComponent(0.3)
        activeLoadImageView.layer.cornerRadius = 2
    }

    private func setupThumb() {
        // 设置白色操作钮
        thumbView.backgroundColor = .white
        thumbView.layer.cornerRadius = thumbSize / 2
        thumbView.isUserInteractionEnabled = false

        // 添加阴影效果
        thumbView.layer.shadowColor = UIColor.black.cgColor
        thumbView.layer.shadowOffset = CGSize(width: 0, height: 2)
        thumbView.layer.shadowRadius = 4
        thumbView.layer.shadowOpacity = 0.2
    }

    // MARK: - Layout

    override func layoutSubviews() {
        super.layoutSubviews()

        // 布局背景视图 - 使用固定的 12px 圆角
        backgroundView.frame = CGRect(x: 0, y: (bounds.height - sliderHeight) / 2,
                                    width: bounds.width, height: sliderHeight)
        backgroundView.layer.cornerRadius = 12

        // 布局激活区域视图 - 根据设计稿，激活区域宽度为 73px
        let activeWidth = max(0, min(CGFloat(value) * bounds.width, bounds.width))
        activeView.frame = CGRect(x: 0, y: (bounds.height - sliderHeight) / 2,
                                width: activeWidth, height: sliderHeight)
        activeView.layer.cornerRadius = 12

        // 布局 ActiveLoad 图片 - 根据设计稿，尺寸为 20x20，居中显示
        let imageSize: CGFloat = 20
        activeLoadImageView.frame = CGRect(x: 8, y: (bounds.height - imageSize) / 2,
                                         width: imageSize, height: imageSize)

        // 布局白色操作钮 - 位于激活区域的右边缘
        let thumbX = max(thumbSize / 2, activeWidth - thumbSize / 2)
        thumbView.frame = CGRect(x: thumbX - thumbSize / 2,
                               y: (bounds.height - thumbSize) / 2,
                               width: thumbSize, height: thumbSize)
        thumbView.layer.cornerRadius = thumbSize / 2
    }

    override var intrinsicContentSize: CGSize {
        return CGSize(width: 90, height: sliderHeight)
    }

    // MARK: - Appearance Update

    private func updateSliderAppearance() {
        setNeedsLayout()

        // 添加动画效果
        UIView.animate(withDuration: 0.2, delay: 0, options: [.curveEaseOut], animations: {
            self.layoutIfNeeded()
        })
    }

    // MARK: - Touch Handling

    override func beginTracking(_ touch: UITouch, with event: UIEvent?) -> Bool {
        let location = touch.location(in: self)

        // 检查触摸是否在滑块范围内
        if bounds.contains(location) {
            isTracking = true
            trackingStartValue = value
            updateValueForTouch(touch)
            sendActions(for: .touchDown)
            return true
        }

        return false
    }

    override func continueTracking(_ touch: UITouch, with event: UIEvent?) -> Bool {
        if isTracking {
            updateValueForTouch(touch)
            sendActions(for: .valueChanged)
            return true
        }
        return false
    }

    override func endTracking(_ touch: UITouch?, with event: UIEvent?) {
        if isTracking {
            isTracking = false
            if let touch = touch {
                updateValueForTouch(touch)
            }
            sendActions(for: [.touchUpInside, .valueChanged])
        }
    }

    override func cancelTracking(with event: UIEvent?) {
        if isTracking {
            isTracking = false
            sendActions(for: .touchCancel)
        }
    }

    private func updateValueForTouch(_ touch: UITouch) {
        let location = touch.location(in: self)
        let percentage = location.x / bounds.width
        let newValue = Float(max(0.0, min(1.0, percentage)))

        if newValue != value {
            value = newValue
        }
    }

    // MARK: - Public Methods

    /// 设置滑块值，可选择是否带动画
    /// - Parameters:
    ///   - value: 新的值 (0.0 - 1.0)
    ///   - animated: 是否使用动画
    func setValue(_ value: Float, animated: Bool) {
        let clampedValue = max(0.0, min(1.0, value))

        if animated {
            UIView.animate(withDuration: 0.3, delay: 0, options: [.curveEaseInOut], animations: {
                self.value = clampedValue
            })
        } else {
            self.value = clampedValue
        }
    }

    /// 设置 ActiveLoad 图标
    /// - Parameter image: 图标图片
    func setActiveLoadImage(_ image: UIImage?) {
        activeLoadImageView.image = image
    }
}

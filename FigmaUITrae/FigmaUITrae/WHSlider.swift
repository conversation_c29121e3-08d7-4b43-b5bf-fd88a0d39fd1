import UIKit

@IBDesignable
class WHSlider: UIControl {

    // MARK: - Properties

    /// 滑块的当前值 (0.0 - 1.0)
    @IBInspectable var value: Float = 0.0 {
        didSet {
            value = max(0.0, min(1.0, value))
            updateSliderAppearance()
        }
    }

    /// 滑块背景色
    @IBInspectable var trackColor: UIColor = UIColor(red: 0x24/255.0, green: 0x26/255.0, blue: 0x2b/255.0, alpha: 1.0) {
        didSet {
            backgroundView.backgroundColor = trackColor
        }
    }

    /// 滑块激活区域颜色
    @IBInspectable var activeColor: UIColor = UIColor(red: 0x35/255.0, green: 0x49/255.0, blue: 0xff/255.0, alpha: 1.0) {
        didSet {
            activeView.backgroundColor = activeColor
        }
    }

    /// 滑块高度
    @IBInspectable var sliderHeight: CGFloat = 20.0 {
        didSet {
            invalidateIntrinsicContentSize()
            setNeedsLayout()
        }
    }

    // MARK: - Private Properties

    private let backgroundView = UIView()
    private let activeView = UIView()
    private let iconImageView = UIImageView()

    private var isTracking = false
    private var trackingStartValue: Float = 0.0

    // MARK: - Initialization

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupViews()
    }

    // MARK: - Setup

    private func setupViews() {
        // 设置背景视图
        backgroundView.backgroundColor = trackColor
        backgroundView.layer.cornerRadius = sliderHeight / 2
        backgroundView.isUserInteractionEnabled = false
        addSubview(backgroundView)

        // 设置激活区域视图
        activeView.backgroundColor = activeColor
        activeView.layer.cornerRadius = sliderHeight / 2
        activeView.isUserInteractionEnabled = false
        addSubview(activeView)

        // 设置图标
        setupIcon()
        addSubview(iconImageView)

        // 初始化外观
        updateSliderAppearance()
    }

    private func setupIcon() {
        // 创建 ActiveLoad 图标 (基于设计稿中的图标)
        iconImageView.contentMode = .scaleAspectFit
        iconImageView.tintColor = .white
        iconImageView.isUserInteractionEnabled = false

        // 如果有具体的图标资源，可以设置：
        // iconImageView.image = UIImage(named: "active_load_icon")

        // 临时使用系统图标作为占位符
        let config = UIImage.SymbolConfiguration(pointSize: 12, weight: .medium)
        iconImageView.image = UIImage(systemName: "bolt.fill", withConfiguration: config)
    }

    // MARK: - Layout

    override func layoutSubviews() {
        super.layoutSubviews()

        let cornerRadius = sliderHeight / 2

        // 布局背景视图
        backgroundView.frame = CGRect(x: 0, y: (bounds.height - sliderHeight) / 2,
                                    width: bounds.width, height: sliderHeight)
        backgroundView.layer.cornerRadius = cornerRadius

        // 布局激活区域视图
        let activeWidth = CGFloat(value) * bounds.width
        activeView.frame = CGRect(x: 0, y: (bounds.height - sliderHeight) / 2,
                                width: activeWidth, height: sliderHeight)
        activeView.layer.cornerRadius = cornerRadius

        // 布局图标
        let iconSize: CGFloat = 16
        iconImageView.frame = CGRect(x: 8, y: (bounds.height - iconSize) / 2,
                                   width: iconSize, height: iconSize)
    }

    override var intrinsicContentSize: CGSize {
        return CGSize(width: 90, height: sliderHeight)
    }

    // MARK: - Appearance Update

    private func updateSliderAppearance() {
        setNeedsLayout()

        // 添加动画效果
        UIView.animate(withDuration: 0.2, delay: 0, options: [.curveEaseOut], animations: {
            self.layoutIfNeeded()
        })
    }

    // MARK: - Touch Handling

    override func beginTracking(_ touch: UITouch, with event: UIEvent?) -> Bool {
        let location = touch.location(in: self)

        // 检查触摸是否在滑块范围内
        if bounds.contains(location) {
            isTracking = true
            trackingStartValue = value
            updateValueForTouch(touch)
            sendActions(for: .touchDown)
            return true
        }

        return false
    }

    override func continueTracking(_ touch: UITouch, with event: UIEvent?) -> Bool {
        if isTracking {
            updateValueForTouch(touch)
            sendActions(for: .valueChanged)
            return true
        }
        return false
    }

    override func endTracking(_ touch: UITouch?, with event: UIEvent?) {
        if isTracking {
            isTracking = false
            if let touch = touch {
                updateValueForTouch(touch)
            }
            sendActions(for: [.touchUpInside, .valueChanged])
        }
    }

    override func cancelTracking(with event: UIEvent?) {
        if isTracking {
            isTracking = false
            sendActions(for: .touchCancel)
        }
    }

    private func updateValueForTouch(_ touch: UITouch) {
        let location = touch.location(in: self)
        let percentage = location.x / bounds.width
        let newValue = Float(max(0.0, min(1.0, percentage)))

        if newValue != value {
            value = newValue
        }
    }

    // MARK: - Public Methods

    /// 设置滑块值，可选择是否带动画
    /// - Parameters:
    ///   - value: 新的值 (0.0 - 1.0)
    ///   - animated: 是否使用动画
    func setValue(_ value: Float, animated: Bool) {
        let clampedValue = max(0.0, min(1.0, value))

        if animated {
            UIView.animate(withDuration: 0.3, delay: 0, options: [.curveEaseInOut], animations: {
                self.value = clampedValue
            })
        } else {
            self.value = clampedValue
        }
    }

    /// 设置自定义图标
    /// - Parameter image: 图标图片
    func setIcon(_ image: UIImage?) {
        iconImageView.image = image
    }
}
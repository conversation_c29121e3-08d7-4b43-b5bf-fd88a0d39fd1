//
//  CustomSliderViewController.swift
//  FigmaUITrae
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/27.
//

import UIKit
import SnapKit

class CustomSliderViewController: UIViewController {
    
    // MARK: - UI Components
    private let titleLabel = UILabel()
    private let descriptionLabel = UILabel()
    private let customSlider = CustomSlider()
    private let valueLabel = UILabel()
    private let containerView = UIView()
    private let resetButton = UIButton(type: .system)
    private let closeButton = UIButton(type: .system)
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupSliderCallback()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        view.backgroundColor = .systemBackground
        
        // 配置标题
        titleLabel.text = "自定义 Slider 演示"
        titleLabel.font = UIFont.boldSystemFont(ofSize: 24)
        titleLabel.textAlignment = .center
        titleLabel.textColor = .label
        
        // 配置描述
        descriptionLabel.text = "这是根据 Figma 设计稿创建的自定义滑块组件，具有深色背景和蓝色活跃轨道。"
        descriptionLabel.font = UIFont.systemFont(ofSize: 16)
        descriptionLabel.textAlignment = .center
        descriptionLabel.numberOfLines = 0
        descriptionLabel.textColor = .secondaryLabel
        
        // 配置自定义滑块
        customSlider.minimumValue = 0.0
        customSlider.maximumValue = 100.0
        customSlider.value = 30.0
        
        // 配置数值标签
        valueLabel.text = "当前值: 30.0"
        valueLabel.font = UIFont.monospacedDigitSystemFont(ofSize: 18, weight: .medium)
        valueLabel.textAlignment = .center
        valueLabel.textColor = .label
        valueLabel.backgroundColor = UIColor.systemGray6
        valueLabel.layer.cornerRadius = 8
        valueLabel.clipsToBounds = true
        
        // 配置重置按钮
        resetButton.setTitle("重置为 50", for: .normal)
        resetButton.titleLabel?.font = UIFont.boldSystemFont(ofSize: 16)
        resetButton.backgroundColor = .systemBlue
        resetButton.setTitleColor(.white, for: .normal)
        resetButton.layer.cornerRadius = 8
        resetButton.addTarget(self, action: #selector(resetButtonTapped), for: .touchUpInside)
        
        // 配置关闭按钮
        closeButton.setTitle("关闭", for: .normal)
        closeButton.titleLabel?.font = UIFont.boldSystemFont(ofSize: 16)
        closeButton.backgroundColor = .systemGray
        closeButton.setTitleColor(.white, for: .normal)
        closeButton.layer.cornerRadius = 8
        closeButton.addTarget(self, action: #selector(closeButtonTapped), for: .touchUpInside)
        
        // 配置容器视图
        containerView.backgroundColor = .systemGray6
        containerView.layer.cornerRadius = 16
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOffset = CGSize(width: 0, height: 4)
        containerView.layer.shadowRadius = 8
        containerView.layer.shadowOpacity = 0.1
        
        // 添加子视图
        view.addSubview(containerView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(descriptionLabel)
        containerView.addSubview(customSlider)
        containerView.addSubview(valueLabel)
        containerView.addSubview(resetButton)
        containerView.addSubview(closeButton)
    }
    
    private func setupConstraints() {
        // 容器视图约束
        containerView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.left.right.equalToSuperview().inset(24)
            make.height.equalTo(500)
        }
        
        // 标题约束
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(32)
            make.left.right.equalToSuperview().inset(20)
        }
        
        // 描述约束
        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(20)
        }
        
        // 自定义滑块约束
        customSlider.snp.makeConstraints { make in
            make.top.equalTo(descriptionLabel.snp.bottom).offset(40)
            make.left.right.equalToSuperview().inset(40)
            make.height.equalTo(48) // 与 Figma 设计稿中的高度一致
        }
        
        // 数值标签约束
        valueLabel.snp.makeConstraints { make in
            make.top.equalTo(customSlider.snp.bottom).offset(24)
            make.centerX.equalToSuperview()
            make.width.equalTo(150)
            make.height.equalTo(40)
        }
        
        // 重置按钮约束
        resetButton.snp.makeConstraints { make in
            make.top.equalTo(valueLabel.snp.bottom).offset(32)
            make.centerX.equalToSuperview()
            make.width.equalTo(120)
            make.height.equalTo(44)
        }
        
        // 关闭按钮约束
        closeButton.snp.makeConstraints { make in
            make.top.equalTo(resetButton.snp.bottom).offset(16)
            make.centerX.equalToSuperview()
            make.width.equalTo(120)
            make.height.equalTo(44)
        }
    }
    
    private func setupSliderCallback() {
        customSlider.valueChangedCallback = { [weak self] value in
            self?.valueLabel.text = String(format: "当前值: %.1f", value)
        }
    }
    
    // MARK: - Actions
    @objc private func resetButtonTapped() {
        customSlider.setValue(50.0, animated: true)
        valueLabel.text = "当前值: 50.0"
        
        // 添加按钮动画
        UIView.animate(withDuration: 0.1, animations: {
            self.resetButton.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.resetButton.transform = CGAffineTransform.identity
            }
        }
    }
    
    @objc private func closeButtonTapped() {
        dismiss(animated: true)
    }
}

// MARK: - Navigation Bar Setup
extension CustomSliderViewController {
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        // 设置导航栏
        navigationController?.navigationBar.prefersLargeTitles = false
        navigationItem.title = "自定义 Slider"
        
        // 添加导航栏关闭按钮
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .done,
            target: self,
            action: #selector(closeButtonTapped)
        )
    }
}
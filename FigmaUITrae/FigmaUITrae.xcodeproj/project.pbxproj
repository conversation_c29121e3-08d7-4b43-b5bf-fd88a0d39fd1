// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		2499C4C42E486B86008A0186 /* CustomSlider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2499C4C22E486B85008A0186 /* CustomSlider.swift */; };
		2499C4C52E486B86008A0186 /* CustomSliderViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2499C4C32E486B86008A0186 /* CustomSliderViewController.swift */; };
		2499C4C82E48811B008A0186 /* WHSlider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2499C4C62E48811B008A0186 /* WHSlider.swift */; };
		2499C4C92E48811B008A0186 /* WHSliderDemoViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2499C4C72E48811B008A0186 /* WHSliderDemoViewController.swift */; };
		249EFF062E43B53800E240ED /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 249EFF052E43B53800E240ED /* AppDelegate.swift */; };
		249EFF082E43B53800E240ED /* SceneDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 249EFF072E43B53800E240ED /* SceneDelegate.swift */; };
		249EFF0A2E43B53800E240ED /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 249EFF092E43B53800E240ED /* ViewController.swift */; };
		249EFF0D2E43B53800E240ED /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 249EFF0B2E43B53800E240ED /* Main.storyboard */; };
		249EFF0F2E43B53E00E240ED /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 249EFF0E2E43B53E00E240ED /* Assets.xcassets */; };
		249EFF122E43B53E00E240ED /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 249EFF102E43B53E00E240ED /* LaunchScreen.storyboard */; };
		249EFF1D2E43B53F00E240ED /* FigmaUITraeTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 249EFF1C2E43B53F00E240ED /* FigmaUITraeTests.swift */; };
		249EFF272E43B53F00E240ED /* FigmaUITraeUITests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 249EFF262E43B53F00E240ED /* FigmaUITraeUITests.swift */; };
		249EFF292E43B53F00E240ED /* FigmaUITraeUITestsLaunchTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 249EFF282E43B53F00E240ED /* FigmaUITraeUITestsLaunchTests.swift */; };
		249EFF722E43BE1900E240ED /* SmartCutofViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 249EFF712E43BE1900E240ED /* SmartCutofViewController.swift */; };
		249EFF732E43BE2000E240ED /* NewViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 249EFF742E43BE2000E240ED /* NewViewController.swift */; };
		4AED6BD9F64D1A144DB6D631 /* Pods_FigmaUITrae.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5D7473C191BD49DA891793DE /* Pods_FigmaUITrae.framework */; };
		64B83868F59FFD6111581354 /* Pods_FigmaUITrae_FigmaUITraeUITests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 154597708D388F668E9E90BD /* Pods_FigmaUITrae_FigmaUITraeUITests.framework */; };
		B66BF05274A644AF1E7F4123 /* Pods_FigmaUITraeTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0BF100636BEAA040D2AB2A3F /* Pods_FigmaUITraeTests.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		249EFF192E43B53F00E240ED /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 249EFEFA2E43B53800E240ED /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 249EFF012E43B53800E240ED;
			remoteInfo = FigmaUITrae;
		};
		249EFF232E43B53F00E240ED /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 249EFEFA2E43B53800E240ED /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 249EFF012E43B53800E240ED;
			remoteInfo = FigmaUITrae;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		0BF100636BEAA040D2AB2A3F /* Pods_FigmaUITraeTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_FigmaUITraeTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		1030D63259577499F6BC8851 /* Pods-FigmaUITraeTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-FigmaUITraeTests.debug.xcconfig"; path = "Target Support Files/Pods-FigmaUITraeTests/Pods-FigmaUITraeTests.debug.xcconfig"; sourceTree = "<group>"; };
		154597708D388F668E9E90BD /* Pods_FigmaUITrae_FigmaUITraeUITests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_FigmaUITrae_FigmaUITraeUITests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		1FEE0277040B938A22F7B60B /* Pods-FigmaUITrae-FigmaUITraeUITests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-FigmaUITrae-FigmaUITraeUITests.release.xcconfig"; path = "Target Support Files/Pods-FigmaUITrae-FigmaUITraeUITests/Pods-FigmaUITrae-FigmaUITraeUITests.release.xcconfig"; sourceTree = "<group>"; };
		24077FEB8449729E40A6D5BA /* Pods-FigmaUITrae-FigmaUITraeUITests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-FigmaUITrae-FigmaUITraeUITests.debug.xcconfig"; path = "Target Support Files/Pods-FigmaUITrae-FigmaUITraeUITests/Pods-FigmaUITrae-FigmaUITraeUITests.debug.xcconfig"; sourceTree = "<group>"; };
		2499C4C22E486B85008A0186 /* CustomSlider.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomSlider.swift; sourceTree = "<group>"; };
		2499C4C32E486B86008A0186 /* CustomSliderViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomSliderViewController.swift; sourceTree = "<group>"; };
		2499C4C62E48811B008A0186 /* WHSlider.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHSlider.swift; sourceTree = "<group>"; };
		2499C4C72E48811B008A0186 /* WHSliderDemoViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHSliderDemoViewController.swift; sourceTree = "<group>"; };
		249EFF022E43B53800E240ED /* FigmaUITrae.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = FigmaUITrae.app; sourceTree = BUILT_PRODUCTS_DIR; };
		249EFF052E43B53800E240ED /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		249EFF072E43B53800E240ED /* SceneDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SceneDelegate.swift; sourceTree = "<group>"; };
		249EFF092E43B53800E240ED /* ViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewController.swift; sourceTree = "<group>"; };
		249EFF0C2E43B53800E240ED /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		249EFF0E2E43B53E00E240ED /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		249EFF112E43B53E00E240ED /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		249EFF132E43B53E00E240ED /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		249EFF182E43B53F00E240ED /* FigmaUITraeTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = FigmaUITraeTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		249EFF1C2E43B53F00E240ED /* FigmaUITraeTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FigmaUITraeTests.swift; sourceTree = "<group>"; };
		249EFF222E43B53F00E240ED /* FigmaUITraeUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = FigmaUITraeUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		249EFF262E43B53F00E240ED /* FigmaUITraeUITests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FigmaUITraeUITests.swift; sourceTree = "<group>"; };
		249EFF282E43B53F00E240ED /* FigmaUITraeUITestsLaunchTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FigmaUITraeUITestsLaunchTests.swift; sourceTree = "<group>"; };
		249EFF712E43BE1900E240ED /* SmartCutofViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmartCutofViewController.swift; sourceTree = "<group>"; };
		249EFF742E43BE2000E240ED /* NewViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NewViewController.swift; sourceTree = "<group>"; };
		5D7473C191BD49DA891793DE /* Pods_FigmaUITrae.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_FigmaUITrae.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		ADC335514EDAC264AF438AFB /* Pods-FigmaUITraeTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-FigmaUITraeTests.release.xcconfig"; path = "Target Support Files/Pods-FigmaUITraeTests/Pods-FigmaUITraeTests.release.xcconfig"; sourceTree = "<group>"; };
		DB621A7C966185B1A69225BB /* Pods-FigmaUITrae.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-FigmaUITrae.debug.xcconfig"; path = "Target Support Files/Pods-FigmaUITrae/Pods-FigmaUITrae.debug.xcconfig"; sourceTree = "<group>"; };
		EAE9D1487BFCEED4803CF3C4 /* Pods-FigmaUITrae.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-FigmaUITrae.release.xcconfig"; path = "Target Support Files/Pods-FigmaUITrae/Pods-FigmaUITrae.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		249EFEFF2E43B53800E240ED /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4AED6BD9F64D1A144DB6D631 /* Pods_FigmaUITrae.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		249EFF152E43B53F00E240ED /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B66BF05274A644AF1E7F4123 /* Pods_FigmaUITraeTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		249EFF1F2E43B53F00E240ED /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				64B83868F59FFD6111581354 /* Pods_FigmaUITrae_FigmaUITraeUITests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		249EFEF92E43B53800E240ED = {
			isa = PBXGroup;
			children = (
				249EFF042E43B53800E240ED /* FigmaUITrae */,
				249EFF1B2E43B53F00E240ED /* FigmaUITraeTests */,
				249EFF252E43B53F00E240ED /* FigmaUITraeUITests */,
				249EFF032E43B53800E240ED /* Products */,
				B61E1CB8B91DFB9FD6F41211 /* Pods */,
				8144C7D2381DF5BA4E414A04 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		249EFF032E43B53800E240ED /* Products */ = {
			isa = PBXGroup;
			children = (
				249EFF022E43B53800E240ED /* FigmaUITrae.app */,
				249EFF182E43B53F00E240ED /* FigmaUITraeTests.xctest */,
				249EFF222E43B53F00E240ED /* FigmaUITraeUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		249EFF042E43B53800E240ED /* FigmaUITrae */ = {
			isa = PBXGroup;
			children = (
				2499C4C62E48811B008A0186 /* WHSlider.swift */,
				2499C4C72E48811B008A0186 /* WHSliderDemoViewController.swift */,
				2499C4C22E486B85008A0186 /* CustomSlider.swift */,
				2499C4C32E486B86008A0186 /* CustomSliderViewController.swift */,
				249EFF712E43BE1900E240ED /* SmartCutofViewController.swift */,
				249EFF742E43BE2000E240ED /* NewViewController.swift */,
				249EFF052E43B53800E240ED /* AppDelegate.swift */,
				249EFF072E43B53800E240ED /* SceneDelegate.swift */,
				249EFF092E43B53800E240ED /* ViewController.swift */,
				249EFF0B2E43B53800E240ED /* Main.storyboard */,
				249EFF0E2E43B53E00E240ED /* Assets.xcassets */,
				249EFF102E43B53E00E240ED /* LaunchScreen.storyboard */,
				249EFF132E43B53E00E240ED /* Info.plist */,
			);
			path = FigmaUITrae;
			sourceTree = "<group>";
		};
		249EFF1B2E43B53F00E240ED /* FigmaUITraeTests */ = {
			isa = PBXGroup;
			children = (
				249EFF1C2E43B53F00E240ED /* FigmaUITraeTests.swift */,
			);
			path = FigmaUITraeTests;
			sourceTree = "<group>";
		};
		249EFF252E43B53F00E240ED /* FigmaUITraeUITests */ = {
			isa = PBXGroup;
			children = (
				249EFF262E43B53F00E240ED /* FigmaUITraeUITests.swift */,
				249EFF282E43B53F00E240ED /* FigmaUITraeUITestsLaunchTests.swift */,
			);
			path = FigmaUITraeUITests;
			sourceTree = "<group>";
		};
		8144C7D2381DF5BA4E414A04 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				5D7473C191BD49DA891793DE /* Pods_FigmaUITrae.framework */,
				154597708D388F668E9E90BD /* Pods_FigmaUITrae_FigmaUITraeUITests.framework */,
				0BF100636BEAA040D2AB2A3F /* Pods_FigmaUITraeTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		B61E1CB8B91DFB9FD6F41211 /* Pods */ = {
			isa = PBXGroup;
			children = (
				DB621A7C966185B1A69225BB /* Pods-FigmaUITrae.debug.xcconfig */,
				EAE9D1487BFCEED4803CF3C4 /* Pods-FigmaUITrae.release.xcconfig */,
				24077FEB8449729E40A6D5BA /* Pods-FigmaUITrae-FigmaUITraeUITests.debug.xcconfig */,
				1FEE0277040B938A22F7B60B /* Pods-FigmaUITrae-FigmaUITraeUITests.release.xcconfig */,
				1030D63259577499F6BC8851 /* Pods-FigmaUITraeTests.debug.xcconfig */,
				ADC335514EDAC264AF438AFB /* Pods-FigmaUITraeTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		249EFF012E43B53800E240ED /* FigmaUITrae */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 249EFF2C2E43B53F00E240ED /* Build configuration list for PBXNativeTarget "FigmaUITrae" */;
			buildPhases = (
				C76447AC28AAC4A1F0B1E2D0 /* [CP] Check Pods Manifest.lock */,
				249EFEFE2E43B53800E240ED /* Sources */,
				249EFEFF2E43B53800E240ED /* Frameworks */,
				249EFF002E43B53800E240ED /* Resources */,
				0149DD221E9A1F64A11E3F1C /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = FigmaUITrae;
			productName = FigmaUITrae;
			productReference = 249EFF022E43B53800E240ED /* FigmaUITrae.app */;
			productType = "com.apple.product-type.application";
		};
		249EFF172E43B53F00E240ED /* FigmaUITraeTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 249EFF2F2E43B53F00E240ED /* Build configuration list for PBXNativeTarget "FigmaUITraeTests" */;
			buildPhases = (
				1C86B9E5394BEB9FFCE989D4 /* [CP] Check Pods Manifest.lock */,
				249EFF142E43B53F00E240ED /* Sources */,
				249EFF152E43B53F00E240ED /* Frameworks */,
				249EFF162E43B53F00E240ED /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				249EFF1A2E43B53F00E240ED /* PBXTargetDependency */,
			);
			name = FigmaUITraeTests;
			productName = FigmaUITraeTests;
			productReference = 249EFF182E43B53F00E240ED /* FigmaUITraeTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		249EFF212E43B53F00E240ED /* FigmaUITraeUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 249EFF322E43B53F00E240ED /* Build configuration list for PBXNativeTarget "FigmaUITraeUITests" */;
			buildPhases = (
				1DEE8B929489CD8BA4B4F686 /* [CP] Check Pods Manifest.lock */,
				249EFF1E2E43B53F00E240ED /* Sources */,
				249EFF1F2E43B53F00E240ED /* Frameworks */,
				249EFF202E43B53F00E240ED /* Resources */,
				845716E448852C6351F0E3C5 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				249EFF242E43B53F00E240ED /* PBXTargetDependency */,
			);
			name = FigmaUITraeUITests;
			productName = FigmaUITraeUITests;
			productReference = 249EFF222E43B53F00E240ED /* FigmaUITraeUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		249EFEFA2E43B53800E240ED /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1420;
				LastUpgradeCheck = 1420;
				TargetAttributes = {
					249EFF012E43B53800E240ED = {
						CreatedOnToolsVersion = 14.2;
					};
					249EFF172E43B53F00E240ED = {
						CreatedOnToolsVersion = 14.2;
						TestTargetID = 249EFF012E43B53800E240ED;
					};
					249EFF212E43B53F00E240ED = {
						CreatedOnToolsVersion = 14.2;
						TestTargetID = 249EFF012E43B53800E240ED;
					};
				};
			};
			buildConfigurationList = 249EFEFD2E43B53800E240ED /* Build configuration list for PBXProject "FigmaUITrae" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 249EFEF92E43B53800E240ED;
			productRefGroup = 249EFF032E43B53800E240ED /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				249EFF012E43B53800E240ED /* FigmaUITrae */,
				249EFF172E43B53F00E240ED /* FigmaUITraeTests */,
				249EFF212E43B53F00E240ED /* FigmaUITraeUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		249EFF002E43B53800E240ED /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				249EFF122E43B53E00E240ED /* LaunchScreen.storyboard in Resources */,
				249EFF0F2E43B53E00E240ED /* Assets.xcassets in Resources */,
				249EFF0D2E43B53800E240ED /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		249EFF162E43B53F00E240ED /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		249EFF202E43B53F00E240ED /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		0149DD221E9A1F64A11E3F1C /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-FigmaUITrae/Pods-FigmaUITrae-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-FigmaUITrae/Pods-FigmaUITrae-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-FigmaUITrae/Pods-FigmaUITrae-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		1C86B9E5394BEB9FFCE989D4 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-FigmaUITraeTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		1DEE8B929489CD8BA4B4F686 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-FigmaUITrae-FigmaUITraeUITests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		845716E448852C6351F0E3C5 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-FigmaUITrae-FigmaUITraeUITests/Pods-FigmaUITrae-FigmaUITraeUITests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-FigmaUITrae-FigmaUITraeUITests/Pods-FigmaUITrae-FigmaUITraeUITests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-FigmaUITrae-FigmaUITraeUITests/Pods-FigmaUITrae-FigmaUITraeUITests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		C76447AC28AAC4A1F0B1E2D0 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-FigmaUITrae-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		249EFEFE2E43B53800E240ED /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				249EFF722E43BE1900E240ED /* SmartCutofViewController.swift in Sources */,
				2499C4C42E486B86008A0186 /* CustomSlider.swift in Sources */,
				249EFF732E43BE2000E240ED /* NewViewController.swift in Sources */,
				2499C4C82E48811B008A0186 /* WHSlider.swift in Sources */,
				249EFF0A2E43B53800E240ED /* ViewController.swift in Sources */,
				2499C4C92E48811B008A0186 /* WHSliderDemoViewController.swift in Sources */,
				249EFF062E43B53800E240ED /* AppDelegate.swift in Sources */,
				2499C4C52E486B86008A0186 /* CustomSliderViewController.swift in Sources */,
				249EFF082E43B53800E240ED /* SceneDelegate.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		249EFF142E43B53F00E240ED /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				249EFF1D2E43B53F00E240ED /* FigmaUITraeTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		249EFF1E2E43B53F00E240ED /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				249EFF272E43B53F00E240ED /* FigmaUITraeUITests.swift in Sources */,
				249EFF292E43B53F00E240ED /* FigmaUITraeUITestsLaunchTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		249EFF1A2E43B53F00E240ED /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 249EFF012E43B53800E240ED /* FigmaUITrae */;
			targetProxy = 249EFF192E43B53F00E240ED /* PBXContainerItemProxy */;
		};
		249EFF242E43B53F00E240ED /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 249EFF012E43B53800E240ED /* FigmaUITrae */;
			targetProxy = 249EFF232E43B53F00E240ED /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		249EFF0B2E43B53800E240ED /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				249EFF0C2E43B53800E240ED /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		249EFF102E43B53E00E240ED /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				249EFF112E43B53E00E240ED /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		249EFF2A2E43B53F00E240ED /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		249EFF2B2E43B53F00E240ED /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		249EFF2D2E43B53F00E240ED /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DB621A7C966185B1A69225BB /* Pods-FigmaUITrae.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 375GT6S3QN;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = FigmaUITrae/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.dehengxu.apps.-figmauitrae.FigmaUITrae";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		249EFF2E2E43B53F00E240ED /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = EAE9D1487BFCEED4803CF3C4 /* Pods-FigmaUITrae.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 375GT6S3QN;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = FigmaUITrae/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.dehengxu.apps.-figmauitrae.FigmaUITrae";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		249EFF302E43B53F00E240ED /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1030D63259577499F6BC8851 /* Pods-FigmaUITraeTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 375GT6S3QN;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.dehengxu.apps.-figmauitrae.FigmaUITraeTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/FigmaUITrae.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/FigmaUITrae";
			};
			name = Debug;
		};
		249EFF312E43B53F00E240ED /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = ADC335514EDAC264AF438AFB /* Pods-FigmaUITraeTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 375GT6S3QN;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.dehengxu.apps.-figmauitrae.FigmaUITraeTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/FigmaUITrae.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/FigmaUITrae";
			};
			name = Release;
		};
		249EFF332E43B53F00E240ED /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 24077FEB8449729E40A6D5BA /* Pods-FigmaUITrae-FigmaUITraeUITests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 375GT6S3QN;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.dehengxu.apps.-figmauitrae.FigmaUITraeUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = FigmaUITrae;
			};
			name = Debug;
		};
		249EFF342E43B53F00E240ED /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1FEE0277040B938A22F7B60B /* Pods-FigmaUITrae-FigmaUITraeUITests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 375GT6S3QN;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.dehengxu.apps.-figmauitrae.FigmaUITraeUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = FigmaUITrae;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		249EFEFD2E43B53800E240ED /* Build configuration list for PBXProject "FigmaUITrae" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				249EFF2A2E43B53F00E240ED /* Debug */,
				249EFF2B2E43B53F00E240ED /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		249EFF2C2E43B53F00E240ED /* Build configuration list for PBXNativeTarget "FigmaUITrae" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				249EFF2D2E43B53F00E240ED /* Debug */,
				249EFF2E2E43B53F00E240ED /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		249EFF2F2E43B53F00E240ED /* Build configuration list for PBXNativeTarget "FigmaUITraeTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				249EFF302E43B53F00E240ED /* Debug */,
				249EFF312E43B53F00E240ED /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		249EFF322E43B53F00E240ED /* Build configuration list for PBXNativeTarget "FigmaUITraeUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				249EFF332E43B53F00E240ED /* Debug */,
				249EFF342E43B53F00E240ED /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 249EFEFA2E43B53800E240ED /* Project object */;
}
